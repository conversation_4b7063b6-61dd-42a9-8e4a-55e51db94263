/* eslint-env browser */
/* global window, document, confirm */

/**
 * HungDong POS Server Control Panel
 * Desktop application for managing NestJS server
 */

// ============================================================================
// CONSTANTS AND CONFIGURATION
// ============================================================================

const CONFIG = {
  API_BASE_URL: 'http://localhost:3001/v1',
  SERVER_PORT: 3001,
  STATUS_UPDATE_INTERVAL: 5000,
  LOG_UPDATE_INTERVAL: 2000,
  MAX_TAURI_INIT_RETRIES: 10,
  // Performance monitoring
  PERFORMANCE_MONITORING: true,
  SERVER_STARTUP_TIMEOUT: 60000, // 60 seconds max
  HEALTH_CHECK_INTERVAL: 1000, // Check every 1 second instead of waiting
  LOG_LEVELS: {
    INFO: { icon: '📝', name: 'INFO' },
    WARN: { icon: '⚠️', name: 'WARN' },
    ERROR: { icon: '❌', name: 'ERROR' },
    DEBUG: { icon: '🔍', name: 'DEBUG' },
  },
};

// ============================================================================
// GLOBAL STATE
// ============================================================================

const AppState = {
  invoke: null,
  isTauriApp: false,
  serverStatus: {
    running: false,
    port: null,
    pid: null,
    uptime: null,
    last_error: null,
  },
  // Performance monitoring
  performanceMetrics: {
    startupStartTime: null,
    startupEndTime: null,
    lastOperationDuration: null,
    memoryUsage: null,
  },
};

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Show error message to user
 * @param {string} message - Error message
 */
function showError(message) {
  console.error('Error:', message);
  const errorDiv = document.createElement('div');
  errorDiv.className = 'error-message';
  errorDiv.textContent = message;
  errorDiv.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #dc3545;
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    z-index: 1000;
    max-width: 300px;
  `;
  document.body.appendChild(errorDiv);
  setTimeout(() => errorDiv.remove(), 5000);
}

/**
 * Show success message to user
 * @param {string} message - Success message
 */
function showSuccess(message) {
  console.log('Success:', message);
  const successDiv = document.createElement('div');
  successDiv.className = 'success-message';
  successDiv.textContent = message;
  successDiv.style.cssText = `
    position: fixed;
    top: 20px;
    right: 20px;
    background: #28a745;
    color: white;
    padding: 10px 15px;
    border-radius: 5px;
    z-index: 1000;
    max-width: 300px;
  `;
  document.body.appendChild(successDiv);
  setTimeout(() => successDiv.remove(), 3000);
}

/**
 * Format uptime in human readable format
 * @param {number} seconds - Uptime in seconds
 * @returns {string} Formatted uptime
 */
function formatUptime(seconds) {
  if (!seconds || seconds < 0) return '-';

  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = Math.floor(seconds % 60);

  if (hours > 0) {
    return `${hours}h ${minutes}m ${secs}s`;
  }
  if (minutes > 0) {
    return `${minutes}m ${secs}s`;
  }
  return `${secs}s`;
}

// ============================================================================
// PERFORMANCE MONITORING
// ============================================================================

/**
 * Start performance monitoring for an operation
 * @param {string} operationName - Name of the operation
 */
function startPerformanceMonitoring(operationName) {
  if (!CONFIG.PERFORMANCE_MONITORING) return;

  AppState.performanceMetrics.startupStartTime = performance.now();
  console.log(`🚀 [PERF] Starting ${operationName}...`);

  // Monitor memory usage if available
  if (performance.memory) {
    AppState.performanceMetrics.memoryUsage = {
      used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
      total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
      limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024),
    };
    console.log(
      `📊 [PERF] Memory usage: ${AppState.performanceMetrics.memoryUsage.used}MB / ${AppState.performanceMetrics.memoryUsage.total}MB`,
    );
  }
}

/**
 * End performance monitoring for an operation
 * @param {string} operationName - Name of the operation
 */
function endPerformanceMonitoring(operationName) {
  if (!CONFIG.PERFORMANCE_MONITORING || !AppState.performanceMetrics.startupStartTime) return;

  AppState.performanceMetrics.startupEndTime = performance.now();
  AppState.performanceMetrics.lastOperationDuration =
    AppState.performanceMetrics.startupEndTime - AppState.performanceMetrics.startupStartTime;

  console.log(
    `✅ [PERF] ${operationName} completed in ${AppState.performanceMetrics.lastOperationDuration.toFixed(2)}ms`,
  );

  // Check memory usage after operation
  if (performance.memory) {
    const currentMemory = {
      used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
      total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
    };
    const memoryDiff = currentMemory.used - AppState.performanceMetrics.memoryUsage.used;
    console.log(`📊 [PERF] Memory after: ${currentMemory.used}MB (${memoryDiff > 0 ? '+' : ''}${memoryDiff}MB)`);
  }

  // Show performance warning if operation took too long
  if (AppState.performanceMetrics.lastOperationDuration > 5000) {
    console.warn(
      `⚠️ [PERF] ${operationName} took ${(AppState.performanceMetrics.lastOperationDuration / 1000).toFixed(1)}s - this may cause UI lag`,
    );
  }
}

/**
 * Monitor system resources during operation
 */
function monitorSystemResources() {
  if (!CONFIG.PERFORMANCE_MONITORING) return;

  // Monitor frame rate
  let frameCount = 0;
  let lastTime = performance.now();

  function countFrames() {
    frameCount++;
    const currentTime = performance.now();

    if (currentTime - lastTime >= 1000) {
      const fps = Math.round((frameCount * 1000) / (currentTime - lastTime));
      if (fps < 30) {
        console.warn(`⚠️ [PERF] Low FPS detected: ${fps} fps`);
      }
      frameCount = 0;
      lastTime = currentTime;
    }

    requestAnimationFrame(countFrames);
  }

  requestAnimationFrame(countFrames);
}

// ============================================================================
// TAURI API INITIALIZATION
// ============================================================================

/**
 * Initialize Tauri API with multiple fallback methods
 * @returns {boolean} Success status
 */
function initializeTauriAPI() {
  try {
    console.log('🔍 Checking for Tauri API...');

    if (typeof window === 'undefined') {
      console.log('❌ Window object not available');
      return false;
    }

    console.log('✅ Window object is available');

    // Try different ways to access Tauri API
    if (window.__TAURI__ && window.__TAURI__.core && window.__TAURI__.core.invoke) {
      AppState.invoke = window.__TAURI__.core.invoke;
      AppState.isTauriApp = true;
      console.log('✅ Tauri API initialized successfully (core.invoke)');
      return true;
    }

    if (window.__TAURI__ && window.__TAURI__.invoke) {
      AppState.invoke = window.__TAURI__.invoke;
      AppState.isTauriApp = true;
      console.log('✅ Tauri API initialized successfully (direct invoke)');
      return true;
    }

    if (window.__TAURI_INVOKE__) {
      AppState.invoke = window.__TAURI_INVOKE__;
      AppState.isTauriApp = true;
      console.log('✅ Tauri API initialized successfully (__TAURI_INVOKE__)');
      return true;
    }

    if (typeof window.invoke === 'function') {
      AppState.invoke = window.invoke;
      AppState.isTauriApp = true;
      console.log('✅ Tauri API initialized successfully (window.invoke)');
      return true;
    }

    if (window.__TAURI_INTERNALS__) {
      return initializeTauri2x();
    }

    console.log('❌ Running in browser mode - Tauri API not available');
    return false;
  } catch (error) {
    console.log('❌ Tauri API not available:', error);
    return false;
  }
}

/**
 * Initialize Tauri 2.x specific API
 * @returns {boolean} Success status
 */
function initializeTauri2x() {
  console.log('🔍 Tauri 2.x detected via __TAURI_INTERNALS__');

  try {
    if (window.__TAURI_INTERNALS__ && typeof window.__TAURI_INTERNALS__.invoke === 'function') {
      AppState.invoke = window.__TAURI_INTERNALS__.invoke;
      AppState.isTauriApp = true;
      console.log('✅ Tauri 2.x API initialized successfully (internals.invoke)');
      return true;
    }

    // Create mock invoke function for development
    console.log('🔧 Creating mock invoke function for Tauri 2.x...');
    AppState.invoke = createMockInvokeFunction();
    AppState.isTauriApp = true;
    console.log('✅ Tauri 2.x API initialized with mock invoke function');
    return true;
  } catch (error) {
    console.log('❌ Error setting up Tauri 2.x invoke:', error);
    AppState.isTauriApp = true;
    return false;
  }
}

/**
 * Create a mock invoke function for development/testing
 * @returns {Function} Mock invoke function
 */
function createMockInvokeFunction() {
  return function mockInvoke(cmd, args = {}) {
    console.log(`🚀 Mock invoking Tauri command: ${cmd}`, args);

    switch (cmd) {
      case 'get_server_status':
        return Promise.resolve({
          running: false,
          port: null,
          pid: null,
          uptime: null,
          last_error: null,
        });
      case 'start_server':
      case 'stop_server':
      case 'restart_server':
        console.log(`📝 Mock ${cmd} command executed`);
        return Promise.resolve(true);
      case 'get_server_logs':
        return Promise.resolve([]);
      case 'open_browser_url':
        window.open(args.url, '_blank');
        return Promise.resolve(true);
      default:
        return Promise.reject(new Error(`Unknown command: ${cmd}`));
    }
  };
}

/**
 * Attempt to initialize Tauri API with retries
 * @returns {Promise<boolean>} Success status
 */
async function initializeTauriWithRetries() {
  let retries = 0;

  while (retries < CONFIG.MAX_TAURI_INIT_RETRIES) {
    if (initializeTauriAPI()) {
      console.log(`✅ Tauri API initialized after ${retries + 1} attempts`);
      return true;
    }

    retries++;
    console.log(`⏳ Tauri API not ready, attempt ${retries}/${CONFIG.MAX_TAURI_INIT_RETRIES}`);

    if (retries < CONFIG.MAX_TAURI_INIT_RETRIES) {
      await new Promise(resolve => setTimeout(resolve, 200 * retries));
    }
  }

  console.log('❌ Failed to initialize Tauri API after all attempts');
  return false;
}

// ============================================================================
// SERVER MANAGEMENT
// ============================================================================

/**
 * Refresh server status from backend with optimized resource usage
 */
async function refreshStatus() {
  try {
    if (AppState.invoke) {
      // Use Tauri API with timeout
      const statusPromise = AppState.invoke('get_server_status');
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Status check timeout')), 5000),
      );

      const status = await Promise.race([statusPromise, timeoutPromise]);
      AppState.serverStatus = status;
    } else {
      // Fallback: Check server status via HTTP with optimized fetch
      try {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 3000); // 3s timeout

        const response = await fetch(`${CONFIG.API_BASE_URL}/health`, {
          method: 'GET',
          signal: controller.signal,
          cache: 'no-cache',
          headers: {
            Accept: 'application/json',
            'Content-Type': 'application/json',
          },
        });

        clearTimeout(timeoutId);

        if (response.ok) {
          const healthData = await response.json();
          AppState.serverStatus = {
            running: true,
            port: CONFIG.SERVER_PORT,
            pid: null,
            uptime: healthData.uptime || 0,
            last_error: null,
          };
        } else {
          throw new Error(`Server responded with status ${response.status}`);
        }
      } catch (fetchError) {
        AppState.serverStatus = {
          running: false,
          port: null,
          pid: null,
          uptime: null,
          last_error: fetchError.name === 'AbortError' ? 'Request timeout' : 'Server not responding',
        };
      }
    }

    // Only update UI if status actually changed to reduce DOM manipulation
    updateUIIfChanged();
  } catch (error) {
    console.error('Failed to get server status:', error);
    AppState.serverStatus = {
      running: false,
      port: null,
      pid: null,
      uptime: null,
      last_error: error.message,
    };
    updateUIIfChanged();

    // Only show error if it's not a timeout during normal operation
    if (!error.message.includes('timeout')) {
      showError(`Failed to get server status: ${error.message}`);
    }
  }
}

/**
 * Toggle server start/stop with performance monitoring
 */
async function toggleService() {
  const actionBtn = document.getElementById('action-btn');
  if (!actionBtn) return;

  const isStarting = !AppState.serverStatus.running;
  const operationName = isStarting ? 'Server Start' : 'Server Stop';

  // Start performance monitoring
  startPerformanceMonitoring(operationName);

  actionBtn.disabled = true;
  actionBtn.textContent = isStarting ? 'Starting...' : 'Stopping...';

  try {
    if (AppState.invoke) {
      // Use Tauri API
      if (AppState.serverStatus.running) {
        await AppState.invoke('stop_server');
        showSuccess('Server stopped successfully');
      } else {
        // For server start, show immediate feedback and start monitoring
        showSuccess('Server start initiated - please wait...');

        // Start the server
        await AppState.invoke('start_server');

        // Wait for server to be actually ready with optimized polling
        await waitForServerReady(actionBtn);
      }
    } else {
      // Fallback: Show message that this requires Tauri
      showError('Server control requires the desktop application. Please use the Tauri app.');
      actionBtn.disabled = false;
      actionBtn.textContent = AppState.serverStatus.running ? 'Stop' : 'Start';
      return;
    }

    // Refresh status immediately
    await refreshStatus();
  } catch (error) {
    console.error('Failed to toggle server:', error);
    showError(`Failed to toggle server: ${error.message}`);
    actionBtn.disabled = false;
    actionBtn.textContent = AppState.serverStatus.running ? 'Stop' : 'Start';
  } finally {
    // End performance monitoring
    endPerformanceMonitoring(operationName);
  }
}

/**
 * Wait for server to be ready with optimized polling
 * @param {HTMLElement} actionBtn - The action button element
 */
async function waitForServerReady(actionBtn) {
  const startTime = Date.now();
  const maxWaitTime = CONFIG.SERVER_STARTUP_TIMEOUT;
  let attempts = 0;

  while (Date.now() - startTime < maxWaitTime) {
    attempts++;

    // Update button text with progress
    const elapsed = Math.round((Date.now() - startTime) / 1000);
    actionBtn.textContent = `Starting... (${elapsed}s)`;

    try {
      // Check if server is responding
      const response = await fetch(`${CONFIG.API_BASE_URL}/health`, {
        method: 'GET',
        timeout: 2000, // 2 second timeout for each check
      });

      if (response.ok) {
        console.log(`✅ [PERF] Server ready after ${elapsed}s (${attempts} attempts)`);
        showSuccess('Server is ready and responding!');
        actionBtn.textContent = 'Stop';
        actionBtn.disabled = false;
        return;
      }
    } catch (error) {
      // Server not ready yet, continue polling
      console.log(`⏳ [PERF] Server not ready yet (attempt ${attempts}, ${elapsed}s)`);
    }

    // Use exponential backoff for polling to reduce resource usage
    const delay = Math.min(1000 + attempts * 200, 3000); // Start at 1s, max 3s
    await new Promise(resolve => setTimeout(resolve, delay));
  }

  // Timeout reached
  console.warn(`⚠️ [PERF] Server startup timeout after ${maxWaitTime / 1000}s`);
  showError('Server startup is taking longer than expected. Please check the logs.');
  actionBtn.textContent = 'Start';
  actionBtn.disabled = false;
}

/**
 * Restart server
 */
async function restartService() {
  const restartBtn = document.getElementById('restart-btn');
  if (!restartBtn) return;

  restartBtn.disabled = true;
  restartBtn.textContent = 'Restarting...';

  try {
    if (AppState.invoke) {
      // Use Tauri API
      await AppState.invoke('restart_server');
      showSuccess('Server restarted successfully');

      // Refresh status after a short delay
      setTimeout(refreshStatus, 2000);
    } else {
      // Fallback: Show message that this requires Tauri
      showError('Server restart requires the desktop application. Please use the Tauri app.');
    }
  } catch (error) {
    console.error('Failed to restart server:', error);
    showError(`Failed to restart server: ${error.message}`);
  } finally {
    setTimeout(() => {
      restartBtn.disabled = false;
      restartBtn.textContent = 'Restart';
    }, 3000);
  }
}

// ============================================================================
// LOG MANAGEMENT
// ============================================================================

/**
 * Show server logs
 */
async function showLogs() {
  const logsContainer = document.getElementById('logs-container');
  const logsElement = document.getElementById('logs');

  if (!logsContainer || !logsElement) return;

  try {
    if (AppState.invoke) {
      // Use Tauri API
      const logs = await AppState.invoke('get_server_logs');

      let logText = '';
      logs.forEach(log => {
        const timestamp = log.timestamp || new Date().toISOString();
        const level = log.level || 'INFO';
        const message = log.message || '';

        const levelConfig = CONFIG.LOG_LEVELS[level] || CONFIG.LOG_LEVELS.INFO;
        logText += `${levelConfig.icon} [${timestamp}] ${level}: ${message}\n`;
      });

      logsElement.textContent = logText || 'No logs available yet. Start the server to see logs.';
    } else {
      // Fallback: Show message that logs require Tauri
      logsElement.textContent =
        'Server logs are only available in the desktop application.\n' +
        'Please use the Tauri app to view detailed logs.';
    }

    logsContainer.style.display = 'block';

    // Auto-scroll to bottom
    setTimeout(() => {
      logsElement.scrollTop = logsElement.scrollHeight;
    }, 100);
  } catch (error) {
    console.error('Failed to get logs:', error);
    logsElement.textContent = `Failed to load logs: ${error.message}`;
    logsContainer.style.display = 'block';
    showError(`Failed to get logs: ${error.message}`);
  }
}

/**
 * Refresh logs without showing the container (for auto-updates)
 */
async function refreshLogs() {
  const logsElement = document.getElementById('logs');
  if (!logsElement) return;

  try {
    if (AppState.invoke) {
      const logs = await AppState.invoke('get_server_logs');

      let logText = '';
      logs.forEach(log => {
        const timestamp = log.timestamp || new Date().toISOString();
        const level = log.level || 'INFO';
        const message = log.message || '';

        const levelConfig = CONFIG.LOG_LEVELS[level] || CONFIG.LOG_LEVELS.INFO;
        logText += `${levelConfig.icon} [${timestamp}] ${level}: ${message}\n`;
      });

      const wasAtBottom = logsElement.scrollTop >= logsElement.scrollHeight - logsElement.clientHeight - 10;

      logsElement.textContent = logText || 'No logs available yet. Start the server to see logs.';

      // Auto-scroll to bottom only if user was already at the bottom
      if (wasAtBottom) {
        setTimeout(() => {
          logsElement.scrollTop = logsElement.scrollHeight;
        }, 50);
      }
    }
  } catch (error) {
    // Silently fail for auto-refresh to avoid spam
    console.log('Auto-refresh logs failed:', error);
  }
}

/**
 * Hide logs container
 */
function hideLogs() {
  const logsContainer = document.getElementById('logs-container');
  if (logsContainer) {
    logsContainer.style.display = 'none';
  }
}

/**
 * Show performance monitor
 */
function showPerformanceMonitor() {
  const performanceContainer = document.getElementById('performance-container');
  if (performanceContainer) {
    performanceContainer.style.display = 'block';
    updatePerformanceMetrics();

    // Start monitoring system resources
    monitorSystemResources();
  }
}

/**
 * Hide performance monitor
 */
function hidePerformanceMonitor() {
  const performanceContainer = document.getElementById('performance-container');
  if (performanceContainer) {
    performanceContainer.style.display = 'none';
  }
}

/**
 * Update performance metrics display
 */
function updatePerformanceMetrics() {
  // Memory usage
  const memoryUsage = document.getElementById('memory-usage');
  const memoryDetail = document.getElementById('memory-detail');

  if (performance.memory && memoryUsage && memoryDetail) {
    const used = Math.round(performance.memory.usedJSHeapSize / 1024 / 1024);
    const total = Math.round(performance.memory.totalJSHeapSize / 1024 / 1024);
    const limit = Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024);

    memoryUsage.textContent = `${used}MB`;
    memoryDetail.textContent = `${total}MB allocated / ${limit}MB limit`;

    // Color coding based on usage
    memoryUsage.className = 'metric-value';
    if (used > limit * 0.8) {
      memoryUsage.classList.add('error');
    } else if (used > limit * 0.6) {
      memoryUsage.classList.add('warning');
    } else {
      memoryUsage.classList.add('good');
    }
  }

  // Last operation
  const lastOperation = document.getElementById('last-operation');
  const operationDuration = document.getElementById('operation-duration');

  if (lastOperation && operationDuration && AppState.performanceMetrics.lastOperationDuration) {
    const duration = AppState.performanceMetrics.lastOperationDuration;
    lastOperation.textContent = `${duration.toFixed(0)}ms`;
    operationDuration.textContent = 'Last server operation';

    // Color coding based on duration
    lastOperation.className = 'metric-value';
    if (duration > 5000) {
      lastOperation.classList.add('error');
    } else if (duration > 2000) {
      lastOperation.classList.add('warning');
    } else {
      lastOperation.classList.add('good');
    }
  }
}

/**
 * Monitor server response time
 */
async function measureServerResponseTime() {
  const serverResponse = document.getElementById('server-response');
  const responseDetail = document.getElementById('response-detail');

  if (!serverResponse || !responseDetail) return;

  const startTime = performance.now();

  try {
    const response = await fetch(`${CONFIG.API_BASE_URL}/health`, {
      method: 'GET',
      cache: 'no-cache',
    });

    const endTime = performance.now();
    const responseTime = endTime - startTime;

    if (response.ok) {
      serverResponse.textContent = `${responseTime.toFixed(0)}ms`;
      responseDetail.textContent = 'Health check response';

      // Color coding based on response time
      serverResponse.className = 'metric-value';
      if (responseTime > 1000) {
        serverResponse.classList.add('error');
      } else if (responseTime > 500) {
        serverResponse.classList.add('warning');
      } else {
        serverResponse.classList.add('good');
      }
    } else {
      serverResponse.textContent = 'Error';
      responseDetail.textContent = `HTTP ${response.status}`;
      serverResponse.className = 'metric-value error';
    }
  } catch (error) {
    serverResponse.textContent = 'Offline';
    responseDetail.textContent = 'Server not responding';
    serverResponse.className = 'metric-value error';
  }
}

// ============================================================================
// BROWSER INTEGRATION
// ============================================================================

/**
 * Open URL in browser
 * @param {string} url - URL to open
 */
async function openBrowser(url) {
  try {
    if (AppState.invoke) {
      // Use Tauri API
      await AppState.invoke('open_browser_url', { url });
    } else {
      // Fallback: Open in current window/tab
      window.open(url, '_blank');
    }
  } catch (error) {
    console.error('Failed to open browser:', error);
    // Fallback: Try window.open
    try {
      window.open(url, '_blank');
    } catch {
      showError(`Failed to open browser: ${error.message}`);
    }
  }
}

/**
 * Open API documentation
 */
function openApiDocs() {
  openBrowser('http://localhost:3001/docs');
}

/**
 * Open Swagger documentation
 */
function openSwagger() {
  openBrowser('http://localhost:3001/docs');
}

// ============================================================================
// UI MANAGEMENT
// ============================================================================

// Store previous status to detect changes
let previousServerStatus = null;

/**
 * Update UI only if server status has changed (optimized for performance)
 */
function updateUIIfChanged() {
  // Check if status actually changed
  if (
    previousServerStatus &&
    previousServerStatus.running === AppState.serverStatus.running &&
    previousServerStatus.port === AppState.serverStatus.port &&
    previousServerStatus.pid === AppState.serverStatus.pid
  ) {
    // Only update uptime if server is running (less DOM manipulation)
    if (AppState.serverStatus.running) {
      updateUptimeOnly();
    }
    return;
  }

  // Status changed, do full UI update
  updateUI();

  // Store current status for next comparison
  previousServerStatus = { ...AppState.serverStatus };
}

/**
 * Update only the uptime display (minimal DOM manipulation)
 */
function updateUptimeOnly() {
  const serviceInfo = document.getElementById('service-info');
  if (!serviceInfo || !AppState.serverStatus.running) return;

  const uptimeText = AppState.serverStatus.uptime ? formatUptime(AppState.serverStatus.uptime) : '-';
  const uptimeDiv = serviceInfo.querySelector('div:last-child');
  if (uptimeDiv && uptimeDiv.textContent.startsWith('Uptime:')) {
    uptimeDiv.textContent = `Uptime: ${uptimeText}`;
  }
}

/**
 * Update UI based on current server status
 */
function updateUI() {
  const statusDot = document.getElementById('status-dot');
  const statusText = document.getElementById('status-text');
  const serviceInfo = document.getElementById('service-info');
  const actionBtn = document.getElementById('action-btn');
  const restartBtn = document.getElementById('restart-btn');
  const nestjsRow = document.getElementById('nestjs-row');
  const trayStatus = document.getElementById('tray-status');

  if (!statusDot || !statusText || !serviceInfo || !actionBtn || !restartBtn || !nestjsRow || !trayStatus) {
    console.warn('Some UI elements not found');
    return;
  }

  if (AppState.serverStatus.running) {
    // Server is running
    statusDot.className = 'status-dot running';
    statusText.textContent = 'Running';
    nestjsRow.className = 'service-row running';

    actionBtn.textContent = 'Stop';
    actionBtn.className = 'btn btn-stop';
    actionBtn.disabled = false;

    restartBtn.disabled = false;

    const uptimeText = AppState.serverStatus.uptime ? formatUptime(AppState.serverStatus.uptime) : '-';
    serviceInfo.innerHTML = `
      <div>Port: ${AppState.serverStatus.port || '-'} | PID: ${AppState.serverStatus.pid || '-'}</div>
      <div>URL: http://localhost:${AppState.serverStatus.port || CONFIG.SERVER_PORT}</div>
      <div>Uptime: ${uptimeText}</div>
    `;

    trayStatus.textContent = 'API: Running';
  } else {
    // Server is stopped
    statusDot.className = 'status-dot stopped';
    statusText.textContent = 'Stopped';
    nestjsRow.className = 'service-row stopped';

    actionBtn.textContent = 'Start';
    actionBtn.className = 'btn btn-start';
    actionBtn.disabled = false;

    restartBtn.disabled = true;

    serviceInfo.innerHTML = `
      <div>Status: Stopped</div>
      <div>Last Error: ${AppState.serverStatus.last_error || 'None'}</div>
    `;

    trayStatus.textContent = 'API: Stopped';
  }
}

/**
 * Load system information
 */
function loadSystemInfo() {
  const systemInfo = document.getElementById('system-info');
  if (!systemInfo) return;

  // Use userAgentData if available, fallback to deprecated platform
  const platform = navigator.userAgentData?.platform || navigator.platform || 'Unknown';
  const mode = AppState.invoke ? 'Desktop App' : 'Browser Mode';

  systemInfo.innerHTML = `Platform: ${platform}<br>Mode: ${mode}`;
}

// ============================================================================
// WINDOW MANAGEMENT
// ============================================================================

/**
 * Minimize to tray (Tauri only)
 */
function minimizeToTray() {
  if (AppState.invoke) {
    // This would need to be implemented in the Tauri backend
    console.log('Minimize to tray functionality would be implemented here');
  } else {
    window.minimize?.();
  }
}

/**
 * Show main panel
 */
function showPanel() {
  // Implementation for showing the main panel
  console.log('Show panel functionality');
}

/**
 * Exit application
 */
function exitApplication() {
  if (confirm('Are you sure you want to exit the application?')) {
    if (AppState.invoke) {
      // This would need to be implemented in the Tauri backend
      console.log('Exit application via Tauri');
    } else {
      window.close();
    }
  }
}

// ============================================================================
// EVENT HANDLING
// ============================================================================

/**
 * Setup event listeners for UI elements
 */
function setupEventListeners() {
  // Header buttons
  const apiDocsBtn = document.getElementById('api-docs-btn');
  const swaggerBtn = document.getElementById('swagger-btn');

  if (apiDocsBtn) apiDocsBtn.addEventListener('click', openApiDocs);
  if (swaggerBtn) swaggerBtn.addEventListener('click', openSwagger);

  // Server control buttons
  const actionBtn = document.getElementById('action-btn');
  const restartBtn = document.getElementById('restart-btn');

  if (actionBtn) actionBtn.addEventListener('click', toggleService);
  if (restartBtn) restartBtn.addEventListener('click', restartService);

  // Quick action cards
  const openApiCard = document.getElementById('open-api-card');
  const apiDocsCard = document.getElementById('api-docs-card');
  const logsCard = document.getElementById('logs-card');
  const refreshCard = document.getElementById('refresh-card');

  if (openApiCard) {
    openApiCard.addEventListener('click', () => openBrowser(`${CONFIG.API_BASE_URL.replace('/v1', '')}/api/v1`));
  }
  if (apiDocsCard) {
    apiDocsCard.addEventListener('click', () => openBrowser(`${CONFIG.API_BASE_URL.replace('/v1', '')}/docs`));
  }
  if (logsCard) logsCard.addEventListener('click', showLogs);
  if (refreshCard) refreshCard.addEventListener('click', refreshStatus);

  // Log controls
  const hideLogsBtn = document.getElementById('hide-logs-btn');
  const performanceBtn = document.getElementById('performance-btn');
  const hidePerformanceBtn = document.getElementById('hide-performance-btn');

  if (hideLogsBtn) hideLogsBtn.addEventListener('click', hideLogs);
  if (performanceBtn) performanceBtn.addEventListener('click', showPerformanceMonitor);
  if (hidePerformanceBtn) hidePerformanceBtn.addEventListener('click', hidePerformanceMonitor);

  // Global actions
  const minimizeBtn = document.getElementById('minimize-btn');
  const exitBtn = document.getElementById('exit-btn');
  const showPanelBtn = document.getElementById('show-panel-btn');

  if (minimizeBtn) minimizeBtn.addEventListener('click', minimizeToTray);
  if (exitBtn) exitBtn.addEventListener('click', exitApplication);
  if (showPanelBtn) showPanelBtn.addEventListener('click', showPanel);
}

// ============================================================================
// APPLICATION INITIALIZATION
// ============================================================================

/**
 * Initialize the application
 */
async function initializeApp() {
  try {
    console.log('🚀 Initializing HungDong POS Server Control Panel...');

    // Initial status check
    await refreshStatus();

    // Set up periodic status updates
    setInterval(refreshStatus, CONFIG.STATUS_UPDATE_INTERVAL);

    // Set up periodic log updates if logs are visible
    setInterval(() => {
      const logsContainer = document.getElementById('logs-container');
      if (logsContainer && logsContainer.style.display === 'block') {
        refreshLogs();
      }
    }, CONFIG.LOG_UPDATE_INTERVAL);

    // Set up periodic performance monitoring if performance monitor is visible
    setInterval(() => {
      const performanceContainer = document.getElementById('performance-container');
      if (performanceContainer && performanceContainer.style.display === 'block') {
        updatePerformanceMetrics();
        measureServerResponseTime();
      }
    }, 1000); // Update every second for real-time monitoring

    // Load system info
    loadSystemInfo();

    console.log('✅ Application initialized successfully');
  } catch (error) {
    console.error('Failed to initialize application:', error);
    showError(`Failed to initialize application: ${error.message}`);
  }
}

/**
 * Main application startup function
 */
async function startApp() {
  console.log('🎯 Starting application initialization');
  console.log('📱 HungDong POS Server Control Panel loaded');

  // Try to initialize Tauri API with retries
  await initializeTauriWithRetries();

  // Initialize the application
  await initializeApp();

  // Setup event listeners
  setupEventListeners();

  console.log('🏁 Application startup complete');
}

// ============================================================================
// APPLICATION ENTRY POINT
// ============================================================================

// Add immediate debug
console.log('🚀 Script loaded - before DOMContentLoaded');
console.log('📄 Document ready state:', document.readyState);

// Check if DOM is already loaded, otherwise wait for it
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', startApp);
} else {
  // DOM is already loaded
  console.log('📄 DOM already loaded, starting app immediately');
  startApp();
}
