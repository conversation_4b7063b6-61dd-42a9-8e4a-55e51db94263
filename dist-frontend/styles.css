* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    padding: 20px;
}

.control-panel {
    max-width: 1000px;
    margin: 0 auto;
    background: white;
    border-radius: 15px;
    box-shadow: 0 20px 60px rgba(0,0,0,0.1);
    overflow: hidden;
}

.header {
    background: linear-gradient(45deg, #2c3e50, #34495e);
    color: white;
    padding: 20px 30px;
    display: flex;
    align-items: center;
    gap: 15px;
    justify-content: space-between;
}

.header h1 {
    font-size: 24px;
    font-weight: 600;
}

.logo {
    width: 40px;
    height: 40px;
    background: #e74c3c;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 18px;
}

.header-actions {
    display: flex;
    gap: 10px;
}

.main-content {
    padding: 30px;
}

.service-row {
    display: grid;
    grid-template-columns: 1fr 1fr 2fr auto auto;
    gap: 15px;
    align-items: center;
    padding: 15px 20px;
    margin-bottom: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #ddd;
    transition: all 0.3s ease;
}

.service-row.running {
    border-left-color: #27ae60;
    background: #d5f4e6;
}

.service-row.stopped {
    border-left-color: #e74c3c;
    background: #fdeaea;
}

.service-name {
    font-weight: 600;
    font-size: 16px;
}

.service-status {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
}

.status-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #bdc3c7;
}

.status-dot.running {
    background: #27ae60;
    box-shadow: 0 0 10px rgba(39, 174, 96, 0.5);
    animation: pulse 2s infinite;
}

.status-dot.stopped {
    background: #e74c3c;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.service-info {
    font-size: 12px;
    color: #7f8c8d;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.btn-start {
    background: #27ae60;
    color: white;
}

.btn-start:hover {
    background: #219a52;
    transform: translateY(-1px);
}

.btn-stop {
    background: #e74c3c;
    color: white;
}

.btn-stop:hover {
    background: #c0392b;
    transform: translateY(-1px);
}

.btn-restart {
    background: #f39c12;
    color: white;
}

.btn-restart:hover {
    background: #d68910;
    transform: translateY(-1px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-1px);
}

.btn-danger {
    background: #dc3545;
    color: white;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-1px);
}

.btn-small {
    padding: 4px 8px;
    font-size: 10px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.header-row {
    display: grid;
    grid-template-columns: 1fr 1fr 2fr auto auto;
    gap: 15px;
    padding: 10px 20px;
    background: #ecf0f1;
    margin-bottom: 15px;
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #7f8c8d;
    border-radius: 8px;
}

.quick-actions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 25px 0;
}

.action-card {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.action-card:hover {
    background: #e9ecef;
    border-color: #007bff;
    transform: translateY(-2px);
}

.action-icon {
    font-size: 24px;
    margin-bottom: 10px;
}

.action-title {
    font-weight: 600;
    margin-bottom: 5px;
}

.action-desc {
    font-size: 12px;
    color: #6c757d;
}

.logs-container {
    display: none;
    margin-top: 25px;
    background: #2c3e50;
    border-radius: 10px;
    padding: 20px;
    max-height: 400px;
    display: flex;
    flex-direction: column;
}

.logs-container[style*="block"] {
    display: flex !important;
}

.logs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    color: white;
    flex-shrink: 0;
}

.logs-controls {
    display: flex;
    gap: 10px;
}

.logs-header h3 {
    margin: 0;
    color: white;
}

#logs {
    background: #34495e;
    border: 1px solid #4a6741;
    border-radius: 6px;
    padding: 15px;
    color: #2ecc71;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    line-height: 1.4;
    white-space: pre-wrap;
    flex: 1;
    overflow-y: auto;
    min-height: 150px;
    max-height: 300px;
    resize: vertical;
}

/* Custom scrollbar for logs */
#logs::-webkit-scrollbar {
    width: 8px;
}

#logs::-webkit-scrollbar-track {
    background: #2c3e50;
    border-radius: 4px;
}

#logs::-webkit-scrollbar-thumb {
    background: #4a6741;
    border-radius: 4px;
}

#logs::-webkit-scrollbar-thumb:hover {
    background: #5a7751;
}

/* Performance Monitor Styles */
.performance-container {
    display: none;
    margin-top: 25px;
    background: #1a252f;
    border-radius: 10px;
    padding: 20px;
    border: 1px solid #34495e;
}

.performance-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    color: white;
}

.performance-header h3 {
    margin: 0;
    color: #3498db;
}

.performance-metrics {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
}

.metric-card {
    background: #2c3e50;
    border-radius: 8px;
    padding: 15px;
    border: 1px solid #34495e;
    text-align: center;
}

.metric-card h4 {
    margin: 0 0 10px 0;
    color: #ecf0f1;
    font-size: 14px;
    font-weight: 600;
}

.metric-value {
    font-size: 24px;
    font-weight: bold;
    color: #2ecc71;
    margin-bottom: 5px;
}

.metric-detail {
    font-size: 12px;
    color: #95a5a6;
    line-height: 1.4;
}

/* Performance status colors */
.metric-value.warning {
    color: #f39c12;
}

.metric-value.error {
    color: #e74c3c;
}

.metric-value.good {
    color: #2ecc71;
}

.logs-container pre {
    color: #2ecc71;
    font-family: 'Courier New', monospace;
    font-size: 11px;
    line-height: 1.4;
    white-space: pre-wrap;
}

.footer {
    background: #f8f9fa;
    padding: 20px 30px;
    border-top: 1px solid #ecf0f1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.footer-info {
    font-size: 12px;
    color: #7f8c8d;
}

.global-actions {
    display: flex;
    gap: 10px;
}

.system-tray {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(255,255,255,0.95);
    padding: 15px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    font-size: 12px;
    min-width: 200px;
}

.minimized .control-panel {
    display: none;
}

.minimized .system-tray {
    display: block !important;
}
