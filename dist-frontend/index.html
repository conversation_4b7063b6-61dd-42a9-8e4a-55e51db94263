<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HungDong POS Server Control Panel</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="control-panel">
        <div class="header">
            <div class="logo">H</div>
            <div>
                <h1>HungDong POS Server Control Panel</h1>
                <div style="font-size: 12px; opacity: 0.8;">Desktop Server Manager</div>
            </div>
            <div class="header-actions">
                <button class="btn btn-secondary" id="api-docs-btn">API Docs</button>
                <button class="btn btn-secondary" id="swagger-btn">Swagger</button>
            </div>
        </div>

        <div class="main-content">
            <div class="header-row">
                <div>Service</div>
                <div>Status</div>
                <div>Information</div>
                <div>Action</div>
                <div>Tools</div>
            </div>

            <div class="service-row" id="nestjs-row">
                <div class="service-name">NestJS API Server</div>
                <div class="service-status">
                    <div class="status-dot" id="status-dot"></div>
                    <span id="status-text">Checking...</span>
                </div>
                <div class="service-info" id="service-info">
                    <div>Port: - | PID: -</div>
                    <div>URL: http://localhost:3001</div>
                    <div>Uptime: -</div>
                </div>
                <button class="btn" id="action-btn" disabled>Loading...</button>
                <button class="btn btn-restart" id="restart-btn" disabled>Restart</button>
            </div>

            <div class="quick-actions">
                <div class="action-card" id="open-api-card">
                    <div class="action-icon">🌐</div>
                    <div class="action-title">Open API</div>
                    <div class="action-desc">Access REST API</div>
                </div>
                <div class="action-card" id="api-docs-card">
                    <div class="action-icon">📚</div>
                    <div class="action-title">API Docs</div>
                    <div class="action-desc">View Swagger Documentation</div>
                </div>
                <div class="action-card" id="logs-card">
                    <div class="action-icon">📋</div>
                    <div class="action-title">View Logs</div>
                    <div class="action-desc">Server Activity Logs</div>
                </div>
                <div class="action-card" id="refresh-card">
                    <div class="action-icon">🔄</div>
                    <div class="action-title">Refresh</div>
                    <div class="action-desc">Update Status</div>
                </div>
            </div>

            <div class="logs-container" id="logs-container" style="display: none;">
                <div class="logs-header">
                    <h3>Server Logs</h3>
                    <div class="logs-controls">
                        <button class="btn btn-small" id="performance-btn">Performance</button>
                        <button class="btn btn-small" id="hide-logs-btn">Hide</button>
                    </div>
                </div>
                <pre id="logs"></pre>
            </div>

            <!-- Performance Monitor -->
            <div id="performance-container" class="performance-container" style="display: none;">
                <div class="performance-header">
                    <h3>Performance Monitor</h3>
                    <button class="btn btn-small" id="hide-performance-btn">Hide</button>
                </div>
                <div class="performance-metrics">
                    <div class="metric-card">
                        <h4>Memory Usage</h4>
                        <div class="metric-value" id="memory-usage">-</div>
                        <div class="metric-detail" id="memory-detail">-</div>
                    </div>
                    <div class="metric-card">
                        <h4>Last Operation</h4>
                        <div class="metric-value" id="last-operation">-</div>
                        <div class="metric-detail" id="operation-duration">-</div>
                    </div>
                    <div class="metric-card">
                        <h4>Frame Rate</h4>
                        <div class="metric-value" id="frame-rate">-</div>
                        <div class="metric-detail" id="frame-detail">-</div>
                    </div>
                    <div class="metric-card">
                        <h4>Server Response</h4>
                        <div class="metric-value" id="server-response">-</div>
                        <div class="metric-detail" id="response-detail">-</div>
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <div class="footer-info">
                <div>HungDong POS Server v0.0.1</div>
                <div id="system-info">Loading system info...</div>
            </div>
            <div class="global-actions">
                <button class="btn btn-secondary" id="minimize-btn">Minimize to Tray</button>
                <button class="btn btn-danger" id="exit-btn">Exit</button>
            </div>
        </div>
    </div>

    <div class="system-tray" id="system-tray" style="display: none;">
        <div style="font-weight: bold; margin-bottom: 5px;">🟢 HungDong POS Server</div>
        <div id="tray-status">API: Checking...</div>
        <div style="margin-top: 8px;">
            <button id="show-panel-btn" style="font-size: 10px; padding: 4px 8px;">Show Panel</button>
        </div>
    </div>

    <script src="app.js"></script>
</body>
</html>
