import { Controller, Get, Res, Param } from '@nestjs/common';
import { Response } from 'express';
import { Public } from '@/common/decorators/public.decorator';
import { join } from 'path';
import { existsSync } from 'fs';

@Controller('admin')
@Public()
export class AdminController {
  @Get()
  serveAdmin(@Res() res: Response) {
    const filePath = join(process.cwd(), 'dist-frontend', 'index.html');
    console.log('Serving admin from:', filePath);
    console.log('File exists:', existsSync(filePath));
    res.sendFile(filePath);
  }

  @Get()
  serveAdminRoute(@Res() res: Response) {
    const filePath = join(process.cwd(), 'dist-frontend', 'index.html');
    console.log('Serving admin from:', filePath);
    console.log('File exists:', existsSync(filePath));
    res.sendFile(filePath);
  }

  @Get(':file')
  serveAdminAssets(@Param('file') file: string, @Res() res: Response) {
    const filePath = join(process.cwd(), 'dist-frontend', file);

    // Security check - only allow specific file types and files
    const allowedFiles = ['styles.css', 'app.js', 'favicon.ico'];

    if (!allowedFiles.includes(file) || !existsSync(filePath)) {
      return res.status(404).send('File not found');
    }

    // Set appropriate content type
    if (file.endsWith('.css')) {
      res.setHeader('Content-Type', 'text/css');
    } else if (file.endsWith('.js')) {
      res.setHeader('Content-Type', 'application/javascript');
    }

    console.log(`Serving admin asset: ${file} from ${filePath}`);
    res.sendFile(filePath);
  }


}
