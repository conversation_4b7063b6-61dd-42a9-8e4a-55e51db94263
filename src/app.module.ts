import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_FILTER, APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { I18nModule } from 'nestjs-i18n';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';

import { appConfig } from '@/config/app.config';
import { i18nConfig } from '@/config/i18n.config';
import { minisearchConfig } from '@/config/minisearch.config';
import { uploadConfig } from '@/config/upload.config';
import { LoggingInterceptor } from '@/common/interceptors/logging.interceptor';
import { UsersModule } from '@/modules/users/users.module';
import { AuthModule } from '@/modules/auth/auth.module';
import { DatabaseModule } from '@/core/database/database.module';
import { TranslationService } from './common/services/translation.service';
import { I18nValidationExceptionFilters } from './core/filtters/i18n-validation-exception.filter';
import { LanguageDetectionService } from './common/services/language-detection.service';
import { GlobalExceptionFilter } from './core/filtters/global-exception.filter';
import { JwtAuthGuard } from './common/guards/jwt-auth.guard';
import { ProductImportsModule } from './modules/product-import/product-imports.module';
import { ErrorFactory } from './core/errors/error.factory';
import { CategoriesModule } from './modules/categories/categories.module';
import { ProductsModule } from './modules/products/products.module';
import { SearchModule } from './modules/search/search.module';
import { UploadModule } from './modules/upload/upload.module';
import { OrdersModule } from './modules/orders/orders.module';
import { HealthModule } from './modules/health/health.module';
import { AdminModule } from './modules/admin/admin.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [appConfig, minisearchConfig, uploadConfig],
      envFilePath: ['.env'],
      validationOptions: {
        allowUnknown: true,
        abortEarly: true,
      },
    }),
    ServeStaticModule.forRoot({
      rootPath: process.env.UPLOAD_DESTINATION ?? join(process.cwd(), 'uploads'),
      serveRoot: process.env.UPLOAD_PUBLIC_PATH ?? '/uploads',
    }),
    I18nModule.forRoot(i18nConfig),
    DatabaseModule,
    ProductImportsModule,
    UsersModule,
    AuthModule,
    CategoriesModule,
    ProductsModule,
    SearchModule,
    UploadModule,
    OrdersModule,
    HealthModule,
    AdminModule,
  ],
  controllers: [],
  providers: [
    ErrorFactory,
    LanguageDetectionService,
    TranslationService,
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    {
      provide: APP_FILTER,
      useClass: GlobalExceptionFilter,
    },
    {
      provide: APP_FILTER,
      useClass: I18nValidationExceptionFilters,
    },
  ],
})
export class AppModule {}
