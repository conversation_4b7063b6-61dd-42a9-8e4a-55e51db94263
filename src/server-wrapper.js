#!/usr/bin/env node

/**
 * Server wrapper to fix crypto and other compatibility issues
 * This file will be the main entry point for the pkg executable
 */

// Fix crypto global issue for pkg
if (!global.crypto) {
  global.crypto = require('crypto');
}

// Fix other global issues that might occur with pkg
if (!global.fetch) {
  // Add fetch polyfill if needed
  try {
    global.fetch = require('node-fetch');
  } catch (error) {
    // Ignore if node-fetch is not available
  }
}

// Set up proper environment
process.env.NODE_ENV = process.env.NODE_ENV || 'production';
process.env.PORT = process.env.PORT || '3001';

// Add current directory to module paths
const path = require('path');
const Module = require('module');

// Ensure the app can find its modules
const originalResolveFilename = Module._resolveFilename;
Module._resolveFilename = function (request, parent, isMain) {
  try {
    return originalResolveFilename(request, parent, isMain);
  } catch (error) {
    // Try to resolve from the current directory
    if (request.startsWith('./') || request.startsWith('../')) {
      const resolvedPath = path.resolve(process.cwd(), request);
      if (require('fs').existsSync(resolvedPath)) {
        return resolvedPath;
      }
    }
    throw error;
  }
};

console.log('🚀 Starting HungDong POS Server...');
console.log('📁 Working directory:', process.cwd());
console.log('🌐 Environment:', process.env.NODE_ENV);
console.log('🔌 Port:', process.env.PORT);

// Load and start the main application
try {
  require('./main');
} catch (error) {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
}
