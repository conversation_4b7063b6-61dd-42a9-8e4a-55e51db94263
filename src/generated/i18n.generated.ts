/* DO NOT EDIT, file generated by nestjs-i18n */

/* eslint-disable */
/* prettier-ignore */
import { Path } from "nestjs-i18n";
/* prettier-ignore */
export type I18nTranslations = {
    "errors": {
        "auth": {
            "INVALID_CREDENTIALS": string;
            "USER_NOT_FOUND": string;
            "USER_ALREADY_EXISTS": string;
            "USERNAME_ALREADY_EXISTS": string;
            "REGISTRATION_FAILED": string;
            "USER_CREATION_FAILED": string;
            "UNAUTHORIZED": string;
            "TOKEN_EXPIRED": string;
            "INVALID_TOKEN": string;
            "INSUFFICIENT_PERMISSIONS": string;
            "ACCOUNT_DISABLED": string;
            "PASSWORD_RESET_REQUIRED": string;
            "INVALID_REFRESH_TOKEN": string;
            "LOGIN_SUCCESS": string;
            "LOGOUT_SUCCESS": string;
            "REGIS<PERSON>ATION_SUCCESS": string;
            "ACCOUNT_DEACTIVATED": string;
        };
        "user": {
            "USERNAME_ALREADY_EXISTS": string;
        };
        "validation": {
            "VALIDATION_ERROR": string;
            "INVALID_INPUT": string;
            "MISSING_REQUIRED_FIELD": string;
            "INVALID_EMAIL_FORMAT": string;
            "INVALID_PASSWORD_FORMAT": string;
            "INVALID_USERNAME_FORMAT": string;
            "INVALID_PHONE_FORMAT": string;
            "INVALID_DATE_FORMAT": string;
            "INVALID_ENUM_VALUE": string;
            "FIELD_TOO_LONG": string;
            "FIELD_TOO_SHORT": string;
        };
        "database": {
            "DATABASE_CONNECTION_ERROR": string;
            "DATABASE_QUERY_ERROR": string;
            "DUPLICATE_ENTRY": string;
            "FOREIGN_KEY_CONSTRAINT": string;
            "TRANSACTION_FAILED": string;
            "RECORD_NOT_FOUND": string;
            "CONCURRENT_UPDATE_ERROR": string;
            "DATABASE_TIMEOUT": string;
        };
        "business": {
            "BUSINESS_RULE_VIOLATION": string;
            "INSUFFICIENT_BALANCE": string;
            "OPERATION_NOT_ALLOWED": string;
            "BUSINESS_RESOURCE_LOCKED": string;
            "QUOTA_EXCEEDED": string;
            "INVALID_STATE_TRANSITION": string;
            "DEPENDENCY_CONFLICT": string;
        };
        "system": {
            "INTERNAL_SERVER_ERROR": string;
            "SERVICE_UNAVAILABLE": string;
            "EXTERNAL_SERVICE_ERROR": string;
            "CONFIGURATION_ERROR": string;
            "FILE_SYSTEM_ERROR": string;
            "NETWORK_ERROR": string;
            "TIMEOUT_ERROR": string;
            "RATE_LIMIT_EXCEEDED": string;
            "URL_NOT_FOUND": string;
            "TYPE_ERROR": string;
            "REFERENCE_ERROR": string;
            "SYNTAX_ERROR": string;
        };
        "resource": {
            "RESOURCE_NOT_FOUND": string;
            "RESOURCE_ALREADY_EXISTS": string;
            "RESOURCE_ACCESS_DENIED": string;
            "RESOURCE_LOCKED": string;
            "RESOURCE_EXPIRED": string;
            "RESOURCE_CORRUPTED": string;
        };
        "product": {
            "PRODUCT_NOT_FOUND": string;
            "PRODUCT_ALREADY_EXISTS": string;
            "INVALID_PRICE": string;
            "INVALID_STOCK_QUANTITY": string;
            "LOW_STOCK_ALERT": string;
            "OUT_OF_STOCK": string;
            "PRODUCT_DISCONTINUED": string;
            "INVALID_PRODUCT_STATUS": string;
            "BULK_OPERATION_FAILED": string;
            "INVALID_SKU": string;
            "INVALID_BARCODE": string;
        };
        "upload": {
            "FILE_TOO_LARGE": string;
            "INVALID_FILE_TYPE": string;
            "TOO_MANY_FILES": string;
            "INVALID_FILENAME": string;
            "UPLOAD_FAILED": string;
            "PROCESSING_FAILED": string;
            "VIRUS_DETECTED": string;
            "FILE_NOT_FOUND": string;
            "FILE_ALREADY_EXISTS": string;
            "UNSUPPORTED_IMAGE_FORMAT": string;
            "IMAGE_PROCESSING_ERROR": string;
            "INSUFFICIENT_STORAGE_SPACE": string;
            "FILE_CORRUPTED": string;
            "UPLOAD_TIMEOUT": string;
            "STORAGE_INIT_ERROR": string;
            "STORAGE_ERROR": string;
        };
    };
    "validation": {
        "invalid": string;
        "common": {
            "required": string;
            "invalid": string;
            "tooLong": string;
            "tooShort": string;
            "invalidFormat": string;
            "mustBeString": string;
            "mustBeNumber": string;
            "mustBeBoolean": string;
            "mustBeArray": string;
            "mustBeObject": string;
            "mustBeUuid": string;
            "mustBeEmail": string;
            "mustBeUrl": string;
            "mustBeDate": string;
            "mustBePositive": string;
            "mustBeNegative": string;
            "mustBeInteger": string;
            "mustBeDecimal": string;
            "mustBeInRange": string;
            "mustBeOneOf": string;
            "mustNotBeEmpty": string;
            "mustBeUnique": string;
            "invalidPattern": string;
            "invalidLength": string;
        };
        "fields": {
            "email": string;
            "password": string;
            "username": string;
            "firstName": string;
            "lastName": string;
            "phone": string;
            "address": string;
            "city": string;
            "country": string;
            "zipCode": string;
            "name": string;
            "description": string;
            "price": string;
            "quantity": string;
            "sku": string;
            "barcode": string;
            "category": string;
            "status": string;
            "type": string;
            "role": string;
            "permissions": string;
            "id": string;
            "createdAt": string;
            "updatedAt": string;
            "deletedAt": string;
        };
        "specific": {
            "password": {
                "tooWeak": string;
                "minLength": string;
                "maxLength": string;
            };
            "email": {
                "invalid": string;
                "alreadyExists": string;
            };
            "username": {
                "invalid": string;
                "minLength": string;
                "maxLength": string;
                "alreadyExists": string;
            };
            "phone": {
                "invalid": string;
                "format": string;
            };
            "category": {
                "name": {
                    "minLength": string;
                    "maxLength": string;
                };
                "description": {
                    "maxLength": string;
                };
            };
        };
    };
};
/* prettier-ignore */
export type I18nPath = Path<I18nTranslations>;
