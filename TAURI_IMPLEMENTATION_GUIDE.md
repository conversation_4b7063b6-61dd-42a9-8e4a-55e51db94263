# HungDong POS Server - Tauri Desktop App Implementation Guide

## Overview

This guide provides step-by-step instructions to convert your existing NestJS application into a Tauri desktop application with a web-based admin interface, similar to XAMPP Control Panel.

## Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                    Tauri Desktop App                        │
├─────────────────────────────────────────────────────────────┤
│  Frontend (Web UI)           │  Backend (Rust)              │
│  - HTML/CSS/JS Admin Panel   │  - Server Management         │
│  - Real-time Status Updates  │  - Process Control           │
│  - Log Viewer                │  - System Integration        │
├─────────────────────────────────────────────────────────────┤
│                    Embedded NestJS Server                   │
│  - REST API                  │  - Database (SQLite)         │
│  - Business Logic            │  - File Uploads              │
│  - Authentication            │  - Static File Serving       │
└─────────────────────────────────────────────────────────────┘
```

## Prerequisites

1. **Node.js** (v18 or higher)
2. **Rust** (latest stable version)
3. **Tauri CLI** (will be installed via npm)
4. **System Dependencies**:
   - Windows: Microsoft C++ Build Tools
   - macOS: Xcode Command Line Tools
   - Linux: Build essentials, webkit2gtk

## Installation Steps

### 1. Install Rust

```bash
# Install Rust
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh
source ~/.cargo/env

# Verify installation
rustc --version
cargo --version
```

### 2. Install System Dependencies

**Windows:**
```bash
# Install Microsoft C++ Build Tools
# Download from: https://visualstudio.microsoft.com/visual-cpp-build-tools/
```

**macOS:**
```bash
xcode-select --install
```

**Linux (Ubuntu/Debian):**
```bash
sudo apt update
sudo apt install libwebkit2gtk-4.0-dev \
    build-essential \
    curl \
    wget \
    libssl-dev \
    libgtk-3-dev \
    libayatana-appindicator3-dev \
    librsvg2-dev
```

### 3. Install Project Dependencies

```bash
# Install Node.js dependencies (including Tauri CLI)
npm install

# Install Rust dependencies
cd src-tauri
cargo fetch
cd ..
```

## Project Structure

```
hungdong-pos-server/
├── src/                          # NestJS source code
├── dist/                         # Compiled NestJS app
├── dist-frontend/                # Admin interface files
│   ├── index.html               # Main admin panel
│   ├── styles.css               # Styling
│   └── app.js                   # Frontend logic
├── src-tauri/                    # Tauri Rust backend
│   ├── src/
│   │   ├── main.rs              # Main Tauri app
│   │   ├── server_manager.rs    # NestJS server management
│   │   └── system_info.rs       # System information
│   ├── Cargo.toml               # Rust dependencies
│   ├── tauri.conf.json          # Tauri configuration
│   └── icons/                   # App icons
├── scripts/
│   ├── build-tauri.js           # Build preparation script
│   └── generate-icons.js        # Icon generation
└── package.json                 # Updated with Tauri scripts
```

## Development Workflow

### 1. Development Mode

```bash
# Start NestJS in development mode
npm run start:dev

# In another terminal, start Tauri development mode
npm run tauri:dev
```

This will:
- Start NestJS server on http://localhost:3000
- Open Tauri window pointing to the development server
- Enable hot reload for both frontend and backend changes

### 2. Building for Production

```bash
# Build the complete application
npm run build:tauri

# Or use the individual steps:
npm run tauri:build
```

This will:
1. Build the NestJS application
2. Prepare the admin interface
3. Create startup scripts
4. Bundle everything into a standalone executable

### 3. Build Output

After building, you'll find:
- **Windows**: `src-tauri/target/release/hungdong-pos-server.exe`
- **macOS**: `src-tauri/target/release/bundle/macos/HungDong POS Server.app`
- **Linux**: `src-tauri/target/release/hungdong-pos-server`

## Features

### Admin Interface Features

1. **Server Status Dashboard**
   - Real-time server status (Running/Stopped)
   - Process ID and port information
   - Uptime tracking
   - Memory usage monitoring

2. **Server Control**
   - Start/Stop server with one click
   - Restart functionality
   - Auto-start on application launch

3. **Quick Actions**
   - Open API endpoints in browser
   - Access Swagger documentation
   - View server logs
   - Refresh status

4. **Log Viewer**
   - Real-time log streaming
   - Filterable log levels
   - Scrollable history

5. **System Integration**
   - Minimize to system tray
   - Desktop notifications
   - Auto-start with Windows (configurable)

### Backend Features

1. **Server Management**
   - Automatic NestJS server lifecycle management
   - Health check monitoring
   - Graceful shutdown handling

2. **Process Control**
   - Cross-platform process management
   - Resource monitoring
   - Error handling and recovery

3. **File System Integration**
   - Embedded database and uploads
   - Configuration management
   - Log file handling

## Configuration

### Environment Variables

The application supports these environment variables:

```bash
NODE_ENV=production
PORT=3000
UPLOAD_DESTINATION=./uploads
DATABASE_PATH=./data/hungdong-pos.sqlite
```

### Tauri Configuration

Key configuration in `src-tauri/tauri.conf.json`:

```json
{
  "productName": "HungDong POS Server",
  "identifier": "com.hungdong.pos-server",
  "bundle": {
    "resources": [
      "../dist/**/*",
      "../data/**/*",
      "../uploads/**/*"
    ]
  }
}
```

## Usage Instructions

### For End Users

1. **Installation**
   - Download the executable for your platform
   - Run the installer (Windows/macOS) or extract (Linux)
   - Launch the application

2. **First Run**
   - The application will auto-start the NestJS server
   - Admin interface opens automatically
   - Server status should show "Running"

3. **Daily Usage**
   - Use the admin panel to monitor server status
   - Access your API at http://localhost:3000/api/v1
   - View API documentation at http://localhost:3000/docs
   - Check logs for troubleshooting

4. **System Tray**
   - Minimize to tray for background operation
   - Right-click tray icon for quick actions
   - Server continues running when minimized

### For Developers

1. **Customization**
   - Modify `dist-frontend/` files for UI changes
   - Update `src-tauri/src/` for backend functionality
   - Adjust `tauri.conf.json` for app settings

2. **Adding Features**
   - Add new Tauri commands in `main.rs`
   - Extend server manager functionality
   - Update frontend to use new commands

3. **Debugging**
   - Use `npm run tauri:build:debug` for debug builds
   - Check console logs in development mode
   - Use Rust debugging tools for backend issues

## Troubleshooting

### Common Issues

1. **Server Won't Start**
   - Check if port 3000 is available
   - Verify Node.js is installed and accessible
   - Check file permissions for database/uploads

2. **Build Failures**
   - Ensure all system dependencies are installed
   - Update Rust to latest stable version
   - Clear cargo cache: `cargo clean`

3. **Runtime Errors**
   - Check application logs in the log viewer
   - Verify database file exists and is writable
   - Ensure all required directories are present

### Log Locations

- **Application Logs**: Visible in the admin interface
- **System Logs**: Platform-specific locations
  - Windows: `%APPDATA%/com.hungdong.pos-server/logs`
  - macOS: `~/Library/Logs/com.hungdong.pos-server`
  - Linux: `~/.local/share/com.hungdong.pos-server/logs`

## Next Steps

1. **Icon Customization**
   - Replace placeholder icons in `src-tauri/icons/`
   - Use tools like [icon.kitchen](https://icon.kitchen/) for generation

2. **Code Signing** (for distribution)
   - Configure certificates in `tauri.conf.json`
   - Set up signing for Windows/macOS

3. **Auto-Updates**
   - Implement Tauri's updater plugin
   - Set up update server infrastructure

4. **Advanced Features**
   - Add database backup/restore
   - Implement configuration management
   - Add plugin system for extensions

## Support

For issues and questions:
1. Check this implementation guide
2. Review Tauri documentation: https://tauri.app/
3. Check NestJS documentation: https://nestjs.com/
4. Create issues in the project repository
