# HungDong POS Server - Desktop Application

A desktop application built with Tauri that provides a XAMPP-like control panel for managing your NestJS POS server.

## Quick Start

### Prerequisites
- Node.js (v18+)
- Rust (latest stable)
- Platform-specific build tools (see [Installation Guide](TAURI_IMPLEMENTATION_GUIDE.md))

### Setup
```bash
# Run the setup script
node scripts/setup-tauri.js

# Start development mode
npm run tauri:dev

# Build for production
npm run build:tauri
```

## Features

✅ **XAMPP-like Control Panel**
- Start/Stop server with one click
- Real-time status monitoring
- Process information display
- Uptime tracking

✅ **Web-based Admin Interface**
- Clean, modern UI design
- Responsive layout
- Real-time updates
- System tray integration

✅ **Server Management**
- Automatic NestJS server lifecycle
- Health check monitoring
- Log viewer with real-time updates
- Graceful shutdown handling

✅ **Desktop Integration**
- Single executable file
- No Node.js installation required
- Auto-start functionality
- System tray support

✅ **Cross-platform Support**
- Windows (.exe)
- macOS (.app)
- Linux (AppImage/deb)

## Architecture

```
Desktop App (Tauri)
├── Frontend (HTML/CSS/JS) → Admin Control Panel
├── Backend (Rust) → Server Management & System Integration
└── Embedded NestJS → Your existing API server
```

## Development Commands

```bash
# Development mode (hot reload)
npm run tauri:dev

# Build for production
npm run build:tauri

# Debug build
npm run tauri:build:debug

# Build preparation only
node scripts/build-tauri.js
```

## File Structure

```
├── dist-frontend/           # Admin interface
│   ├── index.html          # Main control panel
│   ├── styles.css          # UI styling
│   └── app.js              # Frontend logic
├── src-tauri/              # Tauri Rust backend
│   ├── src/
│   │   ├── main.rs         # Main application
│   │   └── server_manager.rs # Server management
│   └── tauri.conf.json     # App configuration
└── scripts/
    ├── build-tauri.js      # Build preparation
    └── setup-tauri.js      # Initial setup
```

## Usage

### For End Users
1. Download and run the executable
2. Server starts automatically
3. Use the control panel to manage the server
4. Access your API at http://localhost:3000

### For Developers
1. Modify `dist-frontend/` for UI changes
2. Update `src-tauri/src/` for backend features
3. Use `npm run tauri:dev` for development

## Build Output

After running `npm run build:tauri`:

- **Windows**: `src-tauri/target/release/hungdong-pos-server.exe`
- **macOS**: `src-tauri/target/release/bundle/macos/HungDong POS Server.app`
- **Linux**: `src-tauri/target/release/hungdong-pos-server`

## Screenshots

### Control Panel
- Server status dashboard
- Start/Stop controls
- Quick action buttons
- Real-time log viewer

### System Tray
- Minimized operation
- Quick status check
- One-click panel access

## Documentation

- [Complete Implementation Guide](TAURI_IMPLEMENTATION_GUIDE.md)
- [Tauri Documentation](https://tauri.app/)
- [NestJS Documentation](https://nestjs.com/)

## Troubleshooting

### Common Issues

**Server won't start:**
- Check if port 3000 is available
- Verify file permissions
- Check logs in the admin panel

**Build fails:**
- Ensure all prerequisites are installed
- Run `cargo clean` in src-tauri/
- Update Rust: `rustup update`

**Runtime errors:**
- Check application logs
- Verify database file exists
- Ensure required directories are present

## Support

1. Check the [Implementation Guide](TAURI_IMPLEMENTATION_GUIDE.md)
2. Review error logs in the admin panel
3. Create an issue in the repository

---

**Note**: This desktop application embeds your existing NestJS server, providing a user-friendly interface similar to XAMPP Control Panel. Users can run your POS server without needing to install Node.js or manage command-line operations.
