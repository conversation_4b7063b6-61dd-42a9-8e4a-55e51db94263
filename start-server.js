#!/usr/bin/env node

/**
 * Production server startup script for Tauri app
 */

const path = require('path');
const fs = require('fs');
const { spawn } = require('child_process');

// Get the directory where this script is located (should be the app bundle directory)
const appDir = __dirname;
console.log('🚀 Starting HungDong POS Server in production mode...');
console.log('📁 App directory:', appDir);

// Set environment variables
process.env.NODE_ENV = 'production';
process.env.PORT = '3001';

// Look for the compiled NestJS application
const serverPath = path.join(appDir, 'dist', 'main.js');
console.log('🔍 Looking for compiled server at:', serverPath);

if (!fs.existsSync(serverPath)) {
  console.error('❌ Error: Compiled server not found at', serverPath);
  console.error('📋 Available files in app directory:');
  try {
    const files = fs.readdirSync(appDir);
    files.forEach(file => {
      const filePath = path.join(appDir, file);
      const stats = fs.statSync(filePath);
      console.error(`   ${stats.isDirectory() ? '📁' : '📄'} ${file}`);
    });
  } catch (error) {
    console.error('   Could not list files:', error.message);
  }
  process.exit(1);
}

console.log('🎯 Starting server with production configuration...');

// Start the NestJS server
const server = spawn('node', [serverPath], {
  stdio: 'inherit',
  cwd: appDir
});

server.on('error', (error) => {
  console.error('Failed to start server:', error);
  process.exit(1);
});

server.on('close', (code) => {
  if (code !== 0) {
    console.error(`❌ Server process exited with code ${code}`);
  } else {
    console.log('✅ Server stopped gracefully');
  }
  process.exit(code);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('🛑 Received SIGINT, shutting down gracefully...');
  server.kill('SIGTERM');
});

process.on('SIGTERM', () => {
  console.log('🛑 Received SIGTERM, shutting down gracefully...');
  server.kill('SIGTERM');
});

console.log(`🎯 Server process started with PID: ${server.pid}`);
console.log('⏳ Server should be available at http://localhost:3001 shortly...');
