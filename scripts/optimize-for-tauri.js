#!/usr/bin/env node

/**
 * Optimization script for Tauri app
 * Pre-compiles NestJS for faster startup times
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

const projectRoot = path.resolve(__dirname, '..');
process.chdir(projectRoot);

console.log('🚀 Optimizing NestJS for Tauri app...');
console.log('📁 Project root:', projectRoot);

// Step 1: Clean previous builds
console.log('🧹 Cleaning previous builds...');
try {
  if (fs.existsSync('dist')) {
    execSync('rm -rf dist', { stdio: 'inherit' });
  }
} catch (error) {
  console.warn('⚠️ Could not clean dist folder:', error.message);
}

// Step 2: Build the application with optimizations
console.log('🔨 Building NestJS with optimizations...');
try {
  execSync('npm run build', { 
    stdio: 'inherit',
    env: {
      ...process.env,
      NODE_ENV: 'production',
      // Webpack optimizations
      NODE_OPTIONS: '--max-old-space-size=2048',
    }
  });
} catch (error) {
  console.error('❌ Build failed:', error.message);
  process.exit(1);
}

// Step 3: Create optimized startup script
console.log('📝 Creating optimized startup script...');
const optimizedMainJs = `
// Optimized main.js for Tauri app
const { performance } = require('perf_hooks');

// Performance monitoring
const startTime = performance.now();
console.log('🚀 Starting optimized NestJS server...');

// Disable unnecessary features for faster startup
process.env.NO_UPDATE_NOTIFIER = '1';
process.env.DISABLE_OPENCOLLECTIVE = '1';
process.env.ADBLOCK = '1';

// Import and start the application
require('./main.js').then(() => {
  const endTime = performance.now();
  console.log(\`✅ Server started in \${(endTime - startTime).toFixed(2)}ms\`);
}).catch(error => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});
`;

fs.writeFileSync(path.join('dist', 'main-optimized.js'), optimizedMainJs);

// Step 4: Copy package.json dependencies info
console.log('📦 Copying package dependencies...');
const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const distPackageJson = {
  name: packageJson.name,
  version: packageJson.version,
  main: 'main-optimized.js',
  dependencies: packageJson.dependencies,
  engines: packageJson.engines,
};

fs.writeFileSync(path.join('dist', 'package.json'), JSON.stringify(distPackageJson, null, 2));

// Step 5: Install production dependencies in dist folder
console.log('📥 Installing production dependencies...');
try {
  execSync('npm install --production --no-optional', { 
    stdio: 'inherit',
    cwd: path.join(projectRoot, 'dist'),
    env: {
      ...process.env,
      NODE_ENV: 'production',
    }
  });
} catch (error) {
  console.warn('⚠️ Could not install production dependencies:', error.message);
}

// Step 6: Create performance report
console.log('📊 Creating performance report...');
const performanceReport = {
  optimizedAt: new Date().toISOString(),
  buildTime: Date.now(),
  optimizations: [
    'Pre-compiled TypeScript to JavaScript',
    'Production dependencies only',
    'Disabled update notifiers and ads',
    'Optimized Node.js memory settings',
    'Custom startup script with performance monitoring'
  ],
  startupScript: 'dist/main-optimized.js',
  recommendedNodeOptions: '--max-old-space-size=512 --optimize-for-size'
};

fs.writeFileSync('performance-report.json', JSON.stringify(performanceReport, null, 2));

console.log('✅ Optimization complete!');
console.log('📊 Performance report saved to performance-report.json');
console.log('🎯 Use the optimized startup script for faster server startup');
console.log('');
console.log('Next steps:');
console.log('1. The Tauri app will now use dist/main-optimized.js for faster startup');
console.log('2. Monitor performance using the Performance Monitor in the UI');
console.log('3. Check performance-report.json for optimization details');
