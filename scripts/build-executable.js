#!/usr/bin/env node

/**
 * Build NestJS server as standalone executable using pkg
 * This creates a single executable file with no external dependencies
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Building NestJS server as standalone executable...');

// Step 1: Install pkg if not already installed
console.log('📦 Checking pkg installation...');
try {
  execSync('npx pkg --version', { stdio: 'pipe' });
  console.log('✅ pkg is available');
} catch (error) {
  console.log('📥 Installing pkg...');
  execSync('npm install -g pkg', { stdio: 'inherit' });
}

// Step 2: Ensure NestJS app is built
console.log('🔨 Building NestJS application...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ NestJS build completed');
} catch (error) {
  console.error('❌ Failed to build NestJS app:', error.message);
  process.exit(1);
}

// Step 3: Create pkg configuration
console.log('⚙️ Creating pkg configuration...');
const pkgConfig = {
  name: "hungdong-pos-server",
  version: "0.0.1",
  main: "src/server-wrapper.js",
  bin: "src/server-wrapper.js",
  pkg: {
    targets: [
      "node18-macos-x64",
      "node18-macos-arm64", 
      "node18-win-x64",
      "node18-linux-x64"
    ],
    outputPath: "executables",
    assets: [
      "dist/**/*",
      "src/server-wrapper.js",
      "src/i18n/**/*",
      "data/**/*",
      "uploads/**/*",
      "node_modules/sqlite3/lib/binding/**/*",
      "node_modules/sharp/lib/**/*"
    ],
    scripts: [
      "dist/**/*.js",
      "src/server-wrapper.js"
    ]
  },
  dependencies: {
    // Only include dependencies that pkg needs to know about
    "sqlite3": require('../package.json').dependencies.sqlite3,
    "sharp": require('../package.json').dependencies.sharp
  }
};

// Write pkg configuration to package.json temporarily
const originalPackageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
const tempPackageJson = {
  ...originalPackageJson,
  ...pkgConfig,
  pkg: pkgConfig.pkg
};

fs.writeFileSync('package.json', JSON.stringify(tempPackageJson, null, 2));

// Step 4: Create executables directory
const executablesDir = path.join(process.cwd(), 'executables');
if (fs.existsSync(executablesDir)) {
  execSync(`rm -rf "${executablesDir}"`, { stdio: 'inherit' });
}
fs.mkdirSync(executablesDir, { recursive: true });

// Step 5: Build executable for current platform
console.log('🔧 Building executable for current platform...');
const platform = process.platform;
const arch = process.arch;

let target;
if (platform === 'darwin') {
  target = arch === 'arm64' ? 'node18-macos-arm64' : 'node18-macos-x64';
} else if (platform === 'win32') {
  target = 'node18-win-x64';
} else {
  target = 'node18-linux-x64';
}

const executableName = platform === 'win32' ? 'hungdong-pos-server.exe' : 'hungdong-pos-server';
const executablePath = path.join(executablesDir, executableName);

try {
  console.log(`🎯 Building for target: ${target}`);
  execSync(`npx pkg . --target ${target} --output "${executablePath}"`, { 
    stdio: 'inherit',
    env: {
      ...process.env,
      NODE_ENV: 'production'
    }
  });
  console.log(`✅ Executable created: ${executablePath}`);
} catch (error) {
  console.error('❌ Failed to build executable:', error.message);
  
  // Restore original package.json
  fs.writeFileSync('package.json', JSON.stringify(originalPackageJson, null, 2));
  process.exit(1);
}

// Step 6: Restore original package.json
console.log('🔄 Restoring original package.json...');
fs.writeFileSync('package.json', JSON.stringify(originalPackageJson, null, 2));

// Step 7: Create bundle directory for Tauri
console.log('📁 Creating Tauri bundle...');
const bundleDir = path.join(process.cwd(), 'tauri-bundle');
if (fs.existsSync(bundleDir)) {
  execSync(`rm -rf "${bundleDir}"`, { stdio: 'inherit' });
}
fs.mkdirSync(bundleDir, { recursive: true });

// Copy executable to bundle
fs.copyFileSync(executablePath, path.join(bundleDir, executableName));

// Make executable on Unix systems
if (platform !== 'win32') {
  execSync(`chmod +x "${path.join(bundleDir, executableName)}"`, { stdio: 'inherit' });
}

// Step 8: Create simple startup script for Tauri
console.log('📝 Creating startup script...');
const startupScript = `#!/usr/bin/env node

/**
 * Startup script for standalone executable
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

const appDir = __dirname;
const executableName = '${executableName}';
const executablePath = path.join(appDir, executableName);

console.log('🚀 Starting HungDong POS Server...');
console.log('📁 App directory:', appDir);
console.log('🎯 Executable:', executablePath);

if (!fs.existsSync(executablePath)) {
  console.error('❌ Server executable not found:', executablePath);
  process.exit(1);
}

// Start the server executable
const serverProcess = spawn(executablePath, [], {
  stdio: 'inherit',
  env: {
    ...process.env,
    NODE_ENV: 'production',
    PORT: '3001'
  },
  cwd: appDir
});

serverProcess.on('error', (error) => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});

serverProcess.on('exit', (code, signal) => {
  if (code !== 0) {
    console.error(\`❌ Server exited with code \${code}\`);
  } else {
    console.log('✅ Server stopped gracefully');
  }
  process.exit(code || 0);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('🛑 Shutting down server...');
  serverProcess.kill('SIGTERM');
});

process.on('SIGTERM', () => {
  console.log('🛑 Shutting down server...');
  serverProcess.kill('SIGTERM');
});

console.log(\`🎯 Server started with PID: \${serverProcess.pid}\`);
console.log('⏳ Server should be available at http://localhost:3001 shortly...');
console.log('🌐 Frontend: http://localhost:3001');
console.log('📊 Admin: http://localhost:3001/admin');
console.log('📚 API Docs: http://localhost:3001/docs');
`;

fs.writeFileSync(path.join(bundleDir, 'start-server.js'), startupScript);

// Step 9: Create data directories
console.log('📁 Creating data directories...');
const dataDirs = ['data', 'uploads', 'logs'];
dataDirs.forEach(dir => {
  const targetDir = path.join(bundleDir, dir);
  fs.mkdirSync(targetDir, { recursive: true });
  
  // Copy existing data if available
  const sourceDir = path.join(process.cwd(), dir);
  if (fs.existsSync(sourceDir)) {
    try {
      execSync(`cp -r "${sourceDir}"/* "${targetDir}/" 2>/dev/null || true`, { stdio: 'inherit' });
    } catch (error) {
      // Ignore errors for empty directories
    }
  }
});

// Step 10: Calculate sizes and show summary
console.log('✅ Executable build completed successfully!');

try {
  const executableStats = fs.statSync(executablePath);
  const executableSize = (executableStats.size / 1024 / 1024).toFixed(1);
  console.log(`📊 Executable size: ${executableSize}MB`);
  
  const bundleSize = execSync(`du -sh "${bundleDir}"`, { encoding: 'utf8' }).split('\t')[0];
  console.log(`📊 Bundle size: ${bundleSize}`);
} catch (error) {
  console.log('📊 Size calculation failed');
}

console.log('📁 Executable location:', executablePath);
console.log('📁 Bundle location:', bundleDir);
console.log('');
console.log('🎯 This solution provides:');
console.log('- ✅ Single executable file (no Node.js required)');
console.log('- ✅ No external dependencies');
console.log('- ✅ No node_modules bundling');
console.log('- ✅ Much smaller size');
console.log('- ✅ Frontend and API on same port');
console.log('');
console.log('📋 Next steps:');
console.log('1. Test executable: cd executables && ./hungdong-pos-server');
console.log('2. Build Tauri app: npm run tauri:build');
console.log('3. The built app will use the standalone executable');
