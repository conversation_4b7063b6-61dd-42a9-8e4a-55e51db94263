#!/usr/bin/env node

/**
 * Create a minimal bundle for Tauri app
 * This creates a lightweight bundle without node_modules
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('📦 Creating minimal bundle for Tauri...');

const bundleDir = path.join(process.cwd(), 'tauri-bundle');

// Step 1: Clean and create bundle directory
console.log('🧹 Cleaning bundle directory...');
if (fs.existsSync(bundleDir)) {
  execSync(`rm -rf "${bundleDir}"`, { stdio: 'inherit' });
}
fs.mkdirSync(bundleDir, { recursive: true });

// Step 2: Copy compiled NestJS application
console.log('📋 Copying compiled application...');
const distPath = path.join(process.cwd(), 'dist');
if (!fs.existsSync(distPath)) {
  console.error('❌ dist folder not found! Run npm run build first');
  process.exit(1);
}

execSync(`cp -r "${distPath}" "${bundleDir}/"`, { stdio: 'inherit' });

// Step 3: Create a self-contained startup script
console.log('📝 Creating self-contained startup script...');
const minimalStartupScript = `#!/usr/bin/env node

/**
 * Self-contained production startup script for Tauri app
 */

const { spawn, execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

// Get the directory where this script is located
const appDir = __dirname;
console.log('🚀 Starting HungDong POS Server in production mode...');
console.log('📁 App directory:', appDir);

// Check if dependencies are installed
const nodeModulesPath = path.join(appDir, 'node_modules');
if (!fs.existsSync(nodeModulesPath)) {
  console.log('📦 Installing dependencies...');
  try {
    execSync('npm install --production --no-optional', {
      stdio: 'inherit',
      cwd: appDir,
      env: {
        ...process.env,
        NODE_ENV: 'production'
      }
    });
  } catch (error) {
    console.error('❌ Failed to install dependencies:', error.message);
    process.exit(1);
  }
}

// Look for the compiled NestJS application
const mainJsPath = path.join(appDir, 'dist', 'main.js');

console.log('🔍 Looking for compiled server at:', mainJsPath);

if (!fs.existsSync(mainJsPath)) {
  console.error('❌ Error: Compiled server not found at', mainJsPath);
  process.exit(1);
}

// Set up production environment
const productionEnv = {
  ...process.env,
  NODE_ENV: 'production',
  PORT: '3001',
};

console.log('🎯 Starting server with production configuration...');

// Start the NestJS server
const serverProcess = spawn('node', [mainJsPath], {
  stdio: 'inherit',
  env: productionEnv,
  cwd: appDir
});

// Handle process events
serverProcess.on('error', (error) => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});

serverProcess.on('exit', (code, signal) => {
  if (code !== 0) {
    console.error(\`❌ Server exited with code \${code} and signal \${signal}\`);
  } else {
    console.log('✅ Server stopped gracefully');
  }
  process.exit(code || 0);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('🛑 Received SIGINT, shutting down gracefully...');
  serverProcess.kill('SIGTERM');
});

process.on('SIGTERM', () => {
  console.log('🛑 Received SIGTERM, shutting down gracefully...');
  serverProcess.kill('SIGTERM');
});

console.log(\`🎯 Server process started with PID: \${serverProcess.pid}\`);
console.log('⏳ Server should be available at http://localhost:3001 shortly...');
`;

fs.writeFileSync(path.join(bundleDir, 'start-server.js'), minimalStartupScript);

// Step 4: Create a minimal package.json with production dependencies
console.log('📄 Creating minimal package.json...');
const originalPackageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

const minimalPackageJson = {
  name: originalPackageJson.name,
  version: originalPackageJson.version,
  description: originalPackageJson.description,
  main: 'start-server.js',
  scripts: {
    start: 'node start-server.js'
  },
  dependencies: {
    // Only the most essential runtime dependencies
    '@nestjs/common': originalPackageJson.dependencies['@nestjs/common'],
    '@nestjs/core': originalPackageJson.dependencies['@nestjs/core'],
    '@nestjs/platform-express': originalPackageJson.dependencies['@nestjs/platform-express'],
    'reflect-metadata': originalPackageJson.dependencies['reflect-metadata'],
    'rxjs': originalPackageJson.dependencies['rxjs'],
  },
  engines: originalPackageJson.engines
};

fs.writeFileSync(
  path.join(bundleDir, 'package.json'),
  JSON.stringify(minimalPackageJson, null, 2)
);

// Step 5: Create essential data directories
console.log('📁 Creating data directories...');
const dataDirs = ['data', 'uploads', 'logs'];
dataDirs.forEach(dir => {
  const targetPath = path.join(bundleDir, dir);
  fs.mkdirSync(targetPath, { recursive: true });
  
  // Copy existing data if it exists
  const sourcePath = path.join(process.cwd(), dir);
  if (fs.existsSync(sourcePath)) {
    try {
      execSync(`cp -r "${sourcePath}"/* "${targetPath}/" 2>/dev/null || true`, { stdio: 'inherit' });
    } catch (error) {
      // Ignore errors for empty directories
    }
  }
});

// Step 6: Copy i18n files if they exist
console.log('🌐 Copying i18n files...');
const i18nPath = path.join(process.cwd(), 'src', 'i18n');
if (fs.existsSync(i18nPath)) {
  const targetI18nPath = path.join(bundleDir, 'i18n');
  execSync(`cp -r "${i18nPath}" "${targetI18nPath}"`, { stdio: 'inherit' });
}

// Step 7: Create bundle info
const bundleInfo = {
  name: 'HungDong POS Server',
  version: '0.0.1',
  bundleDate: new Date().toISOString(),
  nodeVersion: process.version,
  platform: process.platform,
  arch: process.arch,
  bundleType: 'minimal',
  note: 'This bundle requires Node.js and npm dependencies to be available in the project directory'
};

fs.writeFileSync(
  path.join(bundleDir, 'bundle-info.json'),
  JSON.stringify(bundleInfo, null, 2)
);

console.log('✅ Minimal bundle created successfully!');
console.log('📁 Bundle location:', bundleDir);

// Calculate bundle size
try {
  const sizeOutput = execSync(`du -sh "${bundleDir}"`, { encoding: 'utf8' });
  const size = sizeOutput.split('\t')[0];
  console.log(`📊 Bundle size: ${size} (minimal - no node_modules)`);
} catch (error) {
  console.log('📊 Bundle size: Could not calculate');
}

console.log('');
console.log('🎯 This minimal bundle:');
console.log('- Contains only the compiled NestJS application');
console.log('- Requires Node.js to be available on the target system');
console.log('- Uses the project\'s node_modules from the installation directory');
console.log('- Much smaller and faster to build');
console.log('');
console.log('📋 Next steps:');
console.log('1. Run: npm run tauri:build');
console.log('2. Test the built application');
