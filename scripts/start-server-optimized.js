#!/usr/bin/env node

/**
 * Optimized server start script for <PERSON>ri app
 * This starts the NestJS server with performance optimizations
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Ensure we're in the project root
const projectRoot = path.resolve(__dirname, '..');
process.chdir(projectRoot);

console.log('🚀 Starting NestJS server with performance optimizations...');
console.log('📁 Project root:', projectRoot);

// Performance optimizations for Node.js
const optimizedEnv = {
  ...process.env,
  NODE_ENV: 'development',
  PORT: '3001',
  // Memory optimizations
  NODE_OPTIONS: '--max-old-space-size=512 --optimize-for-size',
  // Disable unnecessary features for faster startup
  NO_UPDATE_NOTIFIER: '1',
  DISABLE_OPENCOLLECTIVE: '1',
  ADBLOCK: '1',
  // Faster module resolution
  NODE_PRESERVE_SYMLINKS: '1',
  // Reduce garbage collection overhead
  NODE_GC_INTERVAL: '100',
};

// Check if we can use a faster startup method
const distPath = path.join(projectRoot, 'dist');
const optimizedMainPath = path.join(distPath, 'main-optimized.js');
const mainJsPath = path.join(distPath, 'main.js');

let serverProcess;

if (fs.existsSync(optimizedMainPath)) {
  console.log('🚀 Using optimized pre-compiled dist/main-optimized.js for fastest startup');

  // Start directly from optimized compiled JavaScript (fastest)
  serverProcess = spawn('node', [optimizedMainPath], {
    stdio: 'inherit',
    env: optimizedEnv,
    cwd: distPath // Run from dist directory
  });
} else if (fs.existsSync(mainJsPath)) {
  console.log('📦 Using pre-compiled dist/main.js for faster startup');

  // Start directly from compiled JavaScript (faster)
  // Run from project root so it can find node_modules
  serverProcess = spawn('node', [mainJsPath], {
    stdio: 'inherit',
    env: optimizedEnv,
    cwd: projectRoot
  });
} else {
  console.log('🔨 Compiling and starting with nest start (slower)');
  console.log('💡 Tip: Run "npm run optimize:tauri" for faster startup times');

  // Fallback to nest start but with optimizations
  serverProcess = spawn('npx', ['nest', 'start'], {
    stdio: 'inherit',
    env: optimizedEnv,
    cwd: projectRoot
  });
}

// Handle process events
serverProcess.on('error', (error) => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});

serverProcess.on('exit', (code, signal) => {
  if (code !== 0) {
    console.error(`❌ Server exited with code ${code} and signal ${signal}`);
  } else {
    console.log('✅ Server stopped gracefully');
  }
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('🛑 Received SIGINT, shutting down gracefully...');
  serverProcess.kill('SIGTERM');
});

process.on('SIGTERM', () => {
  console.log('🛑 Received SIGTERM, shutting down gracefully...');
  serverProcess.kill('SIGTERM');
});

console.log(`🎯 Server process started with PID: ${serverProcess.pid}`);
console.log('⏳ Waiting for server to be ready...');
