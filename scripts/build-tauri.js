#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Building HungDong POS Server for Tauri...');

// Step 1: Build NestJS application
console.log('📦 Building NestJS application...');
try {
  execSync('npm run build', { stdio: 'inherit' });
  console.log('✅ NestJS build completed');
} catch (error) {
  console.error('❌ NestJS build failed:', error.message);
  process.exit(1);
}

// Step 2: Ensure dist-frontend directory exists and copy admin files
console.log('📁 Preparing admin interface...');
const distFrontendPath = path.join(process.cwd(), 'dist-frontend');
if (!fs.existsSync(distFrontendPath)) {
  fs.mkdirSync(distFrontendPath, { recursive: true });
}

// Copy admin files if they don't exist
const adminFiles = ['index.html', 'styles.css', 'app.js'];
adminFiles.forEach(file => {
  const sourcePath = path.join(distFrontendPath, file);
  if (!fs.existsSync(sourcePath)) {
    console.log(`⚠️  Admin file ${file} not found in dist-frontend/`);
  }
});

// Step 3: Verify startup script exists and is up to date
console.log('📝 Verifying startup script...');
const startupScriptPath = path.join(process.cwd(), 'start-server.js');
if (fs.existsSync(startupScriptPath)) {
  console.log('✅ Startup script found');

  // Check if it has the correct port
  const scriptContent = fs.readFileSync(startupScriptPath, 'utf8');
  if (scriptContent.includes("PORT = '3001'")) {
    console.log('✅ Startup script has correct port configuration');
  } else {
    console.log('⚠️  Startup script may have incorrect port configuration');
  }
} else {
  console.error('❌ Startup script not found! Please ensure start-server.js exists');
  process.exit(1);
}

// Step 4: Ensure required directories exist
console.log('📂 Ensuring required directories exist...');
const requiredDirs = ['data', 'uploads', 'logs'];
requiredDirs.forEach(dir => {
  const dirPath = path.join(process.cwd(), dir);
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
    console.log(`✅ Created directory: ${dir}`);
  }
});

// Step 5: Build standalone executable
console.log('🚀 Building standalone executable...');
try {
  execSync('node scripts/build-executable.js', { stdio: 'inherit' });
  console.log('✅ Standalone executable created');
} catch (error) {
  console.error('❌ Failed to build executable:', error.message);
  console.log('💡 Falling back to lightweight bundle...');
  try {
    execSync('node scripts/create-lightweight-bundle.js', { stdio: 'inherit' });
    console.log('✅ Lightweight bundle created as fallback');
  } catch (fallbackError) {
    console.error('❌ Failed to create fallback bundle:', fallbackError.message);
    process.exit(1);
  }
}

console.log('🎉 Build preparation completed! Ready for Tauri build.');
console.log('');
console.log('Next steps:');
console.log('1. Run: npm run tauri:build');
console.log('2. Find your executable in src-tauri/target/release/');
