#!/usr/bin/env node

/**
 * Bundle Node.js with <PERSON><PERSON> App
 * Downloads and bundles Node.js binary for self-contained distribution
 */

const fs = require('fs');
const path = require('path');
const https = require('https');
const { execSync } = require('child_process');

console.log('📦 Bundling Node.js with <PERSON>ri App...');

// Configuration
const NODE_VERSION = 'v18.19.0'; // LTS version
const BUNDLE_DIR = path.join(process.cwd(), 'tauri-bundle');
const NODEJS_DIR = path.join(BUNDLE_DIR, 'nodejs');

// Platform-specific Node.js download URLs
const NODEJS_URLS = {
  darwin: {
    x64: `https://nodejs.org/dist/${NODE_VERSION}/node-${NODE_VERSION}-darwin-x64.tar.gz`,
    arm64: `https://nodejs.org/dist/${NODE_VERSION}/node-${NODE_VERSION}-darwin-arm64.tar.gz`
  },
  win32: {
    x64: `https://nodejs.org/dist/${NODE_VERSION}/node-${NODE_VERSION}-win-x64.zip`,
    x86: `https://nodejs.org/dist/${NODE_VERSION}/node-${NODE_VERSION}-win-x86.zip`
  },
  linux: {
    x64: `https://nodejs.org/dist/${NODE_VERSION}/node-${NODE_VERSION}-linux-x64.tar.xz`,
    arm64: `https://nodejs.org/dist/${NODE_VERSION}/node-${NODE_VERSION}-linux-arm64.tar.xz`
  }
};

/**
 * Download file from URL
 */
function downloadFile(url, destination) {
  return new Promise((resolve, reject) => {
    console.log(`📥 Downloading: ${url}`);
    const file = fs.createWriteStream(destination);
    
    https.get(url, (response) => {
      if (response.statusCode === 302 || response.statusCode === 301) {
        // Handle redirect
        return downloadFile(response.headers.location, destination)
          .then(resolve)
          .catch(reject);
      }
      
      if (response.statusCode !== 200) {
        reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
        return;
      }
      
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        console.log(`✅ Downloaded: ${path.basename(destination)}`);
        resolve();
      });
      
      file.on('error', (err) => {
        fs.unlink(destination, () => {}); // Delete partial file
        reject(err);
      });
    }).on('error', reject);
  });
}

/**
 * Extract archive
 */
function extractArchive(archivePath, extractDir) {
  const ext = path.extname(archivePath);
  console.log(`📂 Extracting: ${path.basename(archivePath)}`);
  
  try {
    if (ext === '.gz') {
      execSync(`tar -xzf "${archivePath}" -C "${extractDir}"`, { stdio: 'inherit' });
    } else if (ext === '.xz') {
      execSync(`tar -xJf "${archivePath}" -C "${extractDir}"`, { stdio: 'inherit' });
    } else if (ext === '.zip') {
      execSync(`unzip -q "${archivePath}" -d "${extractDir}"`, { stdio: 'inherit' });
    } else {
      throw new Error(`Unsupported archive format: ${ext}`);
    }
    console.log(`✅ Extracted: ${path.basename(archivePath)}`);
  } catch (error) {
    throw new Error(`Failed to extract ${archivePath}: ${error.message}`);
  }
}

/**
 * Get current platform and architecture
 */
function getPlatformInfo() {
  const platform = process.platform;
  const arch = process.arch;
  
  // Map Node.js arch to download arch
  const archMap = {
    x64: 'x64',
    arm64: 'arm64',
    ia32: 'x86'
  };
  
  const mappedArch = archMap[arch] || arch;
  
  if (!NODEJS_URLS[platform] || !NODEJS_URLS[platform][mappedArch]) {
    throw new Error(`Unsupported platform: ${platform}-${arch}`);
  }
  
  return { platform, arch: mappedArch };
}

/**
 * Main bundling function
 */
async function bundleNodejs() {
  try {
    // Step 1: Create bundle directory
    console.log('📁 Creating bundle directory...');
    if (fs.existsSync(BUNDLE_DIR)) {
      execSync(`rm -rf "${BUNDLE_DIR}"`, { stdio: 'inherit' });
    }
    fs.mkdirSync(BUNDLE_DIR, { recursive: true });
    fs.mkdirSync(NODEJS_DIR, { recursive: true });

    // Step 2: Get platform info
    const { platform, arch } = getPlatformInfo();
    console.log(`🖥️  Platform: ${platform}-${arch}`);

    // Step 3: Download Node.js
    const nodeUrl = NODEJS_URLS[platform][arch];
    const archiveName = path.basename(nodeUrl);
    const archivePath = path.join(NODEJS_DIR, archiveName);
    
    await downloadFile(nodeUrl, archivePath);

    // Step 4: Extract Node.js
    const tempExtractDir = path.join(NODEJS_DIR, 'temp');
    fs.mkdirSync(tempExtractDir, { recursive: true });
    extractArchive(archivePath, tempExtractDir);

    // Step 5: Move Node.js binary to correct location
    const extractedDirs = fs.readdirSync(tempExtractDir);
    const nodeDir = extractedDirs.find(dir => dir.startsWith('node-'));
    
    if (!nodeDir) {
      throw new Error('Could not find extracted Node.js directory');
    }

    const sourcePath = path.join(tempExtractDir, nodeDir);
    const targetPath = path.join(NODEJS_DIR, 'bin');
    
    // Copy the bin directory
    if (fs.existsSync(path.join(sourcePath, 'bin'))) {
      execSync(`cp -r "${path.join(sourcePath, 'bin')}" "${NODEJS_DIR}/"`, { stdio: 'inherit' });
    } else {
      // Windows doesn't have bin directory, node.exe is in root
      fs.mkdirSync(targetPath, { recursive: true });
      const nodeExe = platform === 'win32' ? 'node.exe' : 'node';
      execSync(`cp "${path.join(sourcePath, nodeExe)}" "${path.join(targetPath, nodeExe)}"`, { stdio: 'inherit' });
    }

    // Step 6: Copy essential Node.js files
    const essentialDirs = ['lib', 'include'];
    essentialDirs.forEach(dir => {
      const sourceDir = path.join(sourcePath, dir);
      if (fs.existsSync(sourceDir)) {
        execSync(`cp -r "${sourceDir}" "${NODEJS_DIR}/"`, { stdio: 'inherit' });
      }
    });

    // Step 7: Clean up
    execSync(`rm -rf "${tempExtractDir}" "${archivePath}"`, { stdio: 'inherit' });

    // Step 8: Copy application files
    console.log('📋 Copying application files...');

    // Copy dist folder
    const distSource = path.join(process.cwd(), 'dist');
    if (fs.existsSync(distSource)) {
      execSync(`cp -r "${distSource}" "${BUNDLE_DIR}/"`, { stdio: 'inherit' });
    }

    // Copy node_modules (essential for production)
    console.log('📦 Copying node_modules...');
    const nodeModulesSource = path.join(process.cwd(), 'node_modules');
    if (fs.existsSync(nodeModulesSource)) {
      execSync(`cp -r "${nodeModulesSource}" "${BUNDLE_DIR}/"`, { stdio: 'inherit' });
    } else {
      console.log('⚠️  node_modules not found, installing dependencies...');
      // Create a production package.json and install dependencies
      const originalPackageJson = JSON.parse(fs.readFileSync(path.join(process.cwd(), 'package.json'), 'utf8'));
      const productionPackageJson = {
        name: originalPackageJson.name,
        version: originalPackageJson.version,
        dependencies: originalPackageJson.dependencies
      };
      fs.writeFileSync(path.join(BUNDLE_DIR, 'package.json'), JSON.stringify(productionPackageJson, null, 2));

      // Install dependencies in bundle directory
      execSync('npm install --production --no-optional', {
        stdio: 'inherit',
        cwd: BUNDLE_DIR,
        env: { ...process.env, NODE_ENV: 'production' }
      });
    }

    // Copy package.json
    const packageSource = path.join(process.cwd(), 'package.json');
    if (fs.existsSync(packageSource)) {
      execSync(`cp "${packageSource}" "${BUNDLE_DIR}/"`, { stdio: 'inherit' });
    }

    // Create data directories
    const dataDirs = ['data', 'uploads', 'logs'];
    dataDirs.forEach(dir => {
      fs.mkdirSync(path.join(BUNDLE_DIR, dir), { recursive: true });
    });

    // Step 9: Create startup script that uses bundled Node.js
    console.log('📝 Creating startup script...');
    const startupScript = createStartupScript(platform);
    fs.writeFileSync(path.join(BUNDLE_DIR, 'start-server.js'), startupScript);

    // Step 10: Make Node.js executable
    if (platform !== 'win32') {
      const nodeBinary = path.join(NODEJS_DIR, 'bin', 'node');
      if (fs.existsSync(nodeBinary)) {
        execSync(`chmod +x "${nodeBinary}"`, { stdio: 'inherit' });
      }
    }

    console.log('✅ Node.js bundling completed successfully!');
    console.log(`📁 Bundle location: ${BUNDLE_DIR}`);
    console.log(`🎯 Node.js binary: ${path.join(NODEJS_DIR, 'bin', platform === 'win32' ? 'node.exe' : 'node')}`);
    
    // Calculate bundle size
    try {
      const sizeOutput = execSync(`du -sh "${BUNDLE_DIR}"`, { encoding: 'utf8' });
      const size = sizeOutput.split('\t')[0];
      console.log(`📊 Bundle size: ${size}`);
    } catch (error) {
      console.log('📊 Bundle size: Could not calculate');
    }

  } catch (error) {
    console.error('❌ Failed to bundle Node.js:', error.message);
    process.exit(1);
  }
}

/**
 * Create platform-specific startup script
 */
function createStartupScript(platform) {
  const nodeExecutable = platform === 'win32' ? 'nodejs/bin/node.exe' : 'nodejs/bin/node';
  
  return `#!/usr/bin/env node

/**
 * Self-contained startup script with bundled Node.js
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Ensure crypto module is available globally
if (!global.crypto) {
  global.crypto = require('crypto');
}

// Get the directory where this script is located
const appDir = __dirname;
console.log('🚀 Starting HungDong POS Server with bundled Node.js...');
console.log('📁 App directory:', appDir);

// Use bundled Node.js
const nodePath = path.join(appDir, '${nodeExecutable}');
const mainJsPath = path.join(appDir, 'dist', 'main.js');

console.log('🔍 Using bundled Node.js:', nodePath);
console.log('🔍 Starting server:', mainJsPath);

if (!fs.existsSync(nodePath)) {
  console.error('❌ Bundled Node.js not found at:', nodePath);
  process.exit(1);
}

if (!fs.existsSync(mainJsPath)) {
  console.error('❌ Server file not found at:', mainJsPath);
  process.exit(1);
}

// Set up production environment
const productionEnv = {
  ...process.env,
  NODE_ENV: 'production',
  PORT: '3001',
};

console.log('🎯 Starting server with bundled Node.js...');

// Create a wrapper script to fix crypto issues
const wrapperScript = \`
// Fix crypto global issue
if (!global.crypto) {
  global.crypto = require('crypto');
}

// Load and start the main application
require('\${mainJsPath}');
\`;

const wrapperPath = path.join(appDir, 'server-wrapper.js');
require('fs').writeFileSync(wrapperPath, wrapperScript);

// Start the NestJS server using bundled Node.js with wrapper
const serverProcess = spawn(nodePath, [wrapperPath], {
  stdio: 'inherit',
  env: productionEnv,
  cwd: appDir
});

// Handle process events
serverProcess.on('error', (error) => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});

serverProcess.on('exit', (code, signal) => {
  if (code !== 0) {
    console.error(\`❌ Server exited with code \${code} and signal \${signal}\`);
  } else {
    console.log('✅ Server stopped gracefully');
  }
  process.exit(code || 0);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('🛑 Received SIGINT, shutting down gracefully...');
  serverProcess.kill('SIGTERM');
});

process.on('SIGTERM', () => {
  console.log('🛑 Received SIGTERM, shutting down gracefully...');
  serverProcess.kill('SIGTERM');
});

console.log(\`🎯 Server process started with PID: \${serverProcess.pid}\`);
console.log('⏳ Server should be available at http://localhost:3001 shortly...');
`;
}

// Run the bundling process
if (require.main === module) {
  bundleNodejs();
}

module.exports = { bundleNodejs };
