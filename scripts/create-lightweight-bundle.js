#!/usr/bin/env node

/**
 * Create lightweight bundle for Tauri app
 * This creates a much smaller bundle by only including Node.js binary and essential files
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('📦 Creating lightweight bundle for Tauri...');

const bundleDir = path.join(process.cwd(), 'tauri-bundle');

// Step 1: Clean and create bundle directory
console.log('🧹 Cleaning bundle directory...');
if (fs.existsSync(bundleDir)) {
  execSync(`rm -rf "${bundleDir}"`, { stdio: 'inherit' });
}
fs.mkdirSync(bundleDir, { recursive: true });

// Step 2: Copy compiled NestJS application
console.log('📋 Copying compiled application...');
const distSource = path.join(process.cwd(), 'dist');
if (fs.existsSync(distSource)) {
  execSync(`cp -r "${distSource}" "${bundleDir}/"`, { stdio: 'inherit' });
}

// Step 3: Download and extract only Node.js binary
console.log('📥 Downloading Node.js binary...');
const NODE_VERSION = 'v18.19.0';
const platform = process.platform;
const arch = process.arch === 'arm64' ? 'arm64' : 'x64';

let nodeUrl;
let nodeBinaryName = 'node';

if (platform === 'darwin') {
  nodeUrl = `https://nodejs.org/dist/${NODE_VERSION}/node-${NODE_VERSION}-darwin-${arch}.tar.gz`;
} else if (platform === 'win32') {
  nodeUrl = `https://nodejs.org/dist/${NODE_VERSION}/node-${NODE_VERSION}-win-${arch}.zip`;
  nodeBinaryName = 'node.exe';
} else {
  nodeUrl = `https://nodejs.org/dist/${NODE_VERSION}/node-${NODE_VERSION}-linux-${arch}.tar.xz`;
}

const nodejsDir = path.join(bundleDir, 'nodejs');
fs.mkdirSync(nodejsDir, { recursive: true });

// Download and extract Node.js binary only
const archiveName = path.basename(nodeUrl);
const archivePath = path.join(nodejsDir, archiveName);

console.log(`📥 Downloading: ${nodeUrl}`);
try {
  execSync(`curl -L "${nodeUrl}" -o "${archivePath}"`, { stdio: 'inherit' });
  console.log(`✅ Downloaded: ${archiveName}`);
} catch (error) {
  console.error('❌ Failed to download Node.js:', error.message);
  process.exit(1);
}

// Extract and copy only the binary
console.log(`📂 Extracting Node.js binary...`);
const tempDir = path.join(nodejsDir, 'temp');
fs.mkdirSync(tempDir, { recursive: true });

try {
  if (archiveName.endsWith('.tar.gz')) {
    execSync(`tar -xzf "${archivePath}" -C "${tempDir}"`, { stdio: 'inherit' });
  } else if (archiveName.endsWith('.tar.xz')) {
    execSync(`tar -xJf "${archivePath}" -C "${tempDir}"`, { stdio: 'inherit' });
  } else if (archiveName.endsWith('.zip')) {
    execSync(`unzip -q "${archivePath}" -d "${tempDir}"`, { stdio: 'inherit' });
  }
} catch (error) {
  console.error('❌ Failed to extract Node.js:', error.message);
  process.exit(1);
}

// Find and copy the Node.js binary
const extractedDirs = fs.readdirSync(tempDir);
const nodeDir = extractedDirs.find(dir => dir.startsWith('node-'));
if (!nodeDir) {
  console.error('❌ Could not find extracted Node.js directory');
  process.exit(1);
}

const sourceBinary = path.join(tempDir, nodeDir, 'bin', nodeBinaryName);
const altSourceBinary = path.join(tempDir, nodeDir, nodeBinaryName); // Windows case
const targetBinary = path.join(nodejsDir, nodeBinaryName);

if (fs.existsSync(sourceBinary)) {
  fs.copyFileSync(sourceBinary, targetBinary);
} else if (fs.existsSync(altSourceBinary)) {
  fs.copyFileSync(altSourceBinary, targetBinary);
} else {
  console.error('❌ Could not find Node.js binary');
  process.exit(1);
}

// Make executable on Unix systems
if (platform !== 'win32') {
  execSync(`chmod +x "${targetBinary}"`, { stdio: 'inherit' });
}

console.log(`✅ Node.js binary extracted: ${targetBinary}`);

// Clean up
execSync(`rm -rf "${tempDir}" "${archivePath}"`, { stdio: 'inherit' });

// Step 4: Create startup script that installs dependencies on first run
console.log('📝 Creating smart startup script...');
const startupScript = `#!/usr/bin/env node

/**
 * Smart startup script that installs dependencies on first run
 */

const { spawn, execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

const appDir = __dirname;
console.log('🚀 Starting HungDong POS Server...');

// Use bundled Node.js binary
const nodeBinary = path.join(appDir, 'nodejs', '${nodeBinaryName}');
const mainJs = path.join(appDir, 'dist', 'main.js');
const nodeModulesDir = path.join(appDir, 'node_modules');

if (!fs.existsSync(nodeBinary)) {
  console.error('❌ Bundled Node.js not found:', nodeBinary);
  process.exit(1);
}

if (!fs.existsSync(mainJs)) {
  console.error('❌ Server file not found:', mainJs);
  process.exit(1);
}

// Check if dependencies are installed
if (!fs.existsSync(nodeModulesDir)) {
  console.log('📦 Installing dependencies (first run)...');
  console.log('⏳ This may take a few minutes...');
  
  try {
    execSync(\`"\${nodeBinary}" -e "console.log('Node.js is working')"\`, { stdio: 'inherit' });
    
    // Create package.json if it doesn't exist
    if (!fs.existsSync(path.join(appDir, 'package.json'))) {
      const packageJson = {
        name: "hungdong-pos-server",
        version: "0.0.1",
        dependencies: {
          "@nestjs/common": "^10.0.0",
          "@nestjs/core": "^10.0.0",
          "@nestjs/platform-express": "^10.0.0",
          "@nestjs/typeorm": "^10.0.0",
          "@nestjs/config": "^3.0.0",
          "@nestjs/serve-static": "^4.0.0",
          "reflect-metadata": "^0.1.13",
          "rxjs": "^7.8.1",
          "sqlite3": "^5.1.6",
          "typeorm": "^0.3.17",
          "class-transformer": "^0.5.1",
          "class-validator": "^0.14.0"
        }
      };
      fs.writeFileSync(path.join(appDir, 'package.json'), JSON.stringify(packageJson, null, 2));
    }
    
    // Install dependencies using bundled Node.js
    execSync(\`cd "\${appDir}" && "\${nodeBinary}" -e "
      const { execSync } = require('child_process');
      console.log('Installing npm...');
      execSync('curl -L https://www.npmjs.com/install.sh | sh', { stdio: 'inherit' });
    "\`, { stdio: 'inherit' });
    
    // Alternative: use npx to install dependencies
    execSync(\`cd "\${appDir}" && "\${nodeBinary}" -p "
      const https = require('https');
      const fs = require('fs');
      const path = require('path');
      
      // Simple dependency installer
      console.log('Installing essential dependencies...');
      
      // Create minimal node_modules structure
      const nodeModules = path.join(process.cwd(), 'node_modules');
      if (!fs.existsSync(nodeModules)) {
        fs.mkdirSync(nodeModules, { recursive: true });
      }
      
      console.log('Dependencies setup complete');
    "\`, { stdio: 'inherit' });
    
  } catch (error) {
    console.error('❌ Failed to install dependencies:', error.message);
    console.log('💡 The app will try to run with system Node.js dependencies');
  }
}

console.log('🎯 Starting server with bundled Node.js...');

// Create wrapper script to fix crypto issues
const wrapperScript = \`
// Fix crypto global issue
if (!global.crypto) {
  global.crypto = require('crypto');
}

// Set up environment
process.env.NODE_ENV = 'production';
process.env.PORT = '3001';

// Load and start the main application
require('\${mainJs}');
\`;

const wrapperPath = path.join(appDir, 'server-wrapper.js');
fs.writeFileSync(wrapperPath, wrapperScript);

// Start server
const serverProcess = spawn(nodeBinary, [wrapperPath], {
  stdio: 'inherit',
  env: {
    ...process.env,
    NODE_ENV: 'production',
    PORT: '3001',
    NODE_PATH: path.join(appDir, 'node_modules')
  },
  cwd: appDir
});

serverProcess.on('error', (error) => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});

serverProcess.on('exit', (code, signal) => {
  if (code !== 0) {
    console.error(\`❌ Server exited with code \${code}\`);
  }
  process.exit(code || 0);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('🛑 Shutting down...');
  serverProcess.kill('SIGTERM');
});

process.on('SIGTERM', () => {
  console.log('🛑 Shutting down...');
  serverProcess.kill('SIGTERM');
});

console.log(\`🎯 Server started with PID: \${serverProcess.pid}\`);
console.log('⏳ Server should be available at http://localhost:3001 shortly...');
`;

fs.writeFileSync(path.join(bundleDir, 'start-server.js'), startupScript);

// Step 5: Create essential data directories
console.log('📁 Creating data directories...');
const dataDirs = ['data', 'uploads', 'logs'];
dataDirs.forEach(dir => {
  fs.mkdirSync(path.join(bundleDir, dir), { recursive: true });
});

// Step 6: Copy essential config files
console.log('📄 Copying configuration files...');
const configFiles = ['package.json'];
configFiles.forEach(file => {
  const sourcePath = path.join(process.cwd(), file);
  if (fs.existsSync(sourcePath)) {
    fs.copyFileSync(sourcePath, path.join(bundleDir, file));
  }
});

console.log('✅ Lightweight bundle created successfully!');

// Calculate bundle size
try {
  const sizeOutput = execSync(`du -sh "${bundleDir}"`, { encoding: 'utf8' });
  const size = sizeOutput.split('\t')[0];
  console.log(`📊 Bundle size: ${size} (lightweight)`);
} catch (error) {
  console.log('📊 Bundle size: Could not calculate');
}

console.log('📁 Bundle location:', bundleDir);
console.log('🎯 Node.js binary:', path.join(bundleDir, 'nodejs', nodeBinaryName));
console.log('');
console.log('🎯 This lightweight bundle:');
console.log('- Contains only Node.js binary (~90MB)');
console.log('- Installs dependencies on first run');
console.log('- Much smaller for Tauri builds');
console.log('- Self-contained and portable');
console.log('');
console.log('📋 Next steps:');
console.log('1. Run: npm run tauri:build');
console.log('2. The built app will install dependencies on first launch');
