#!/usr/bin/env node

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 HungDong POS Server - Tauri Setup');
console.log('=====================================');

function runCommand(command, description) {
  console.log(`\n📦 ${description}...`);
  try {
    execSync(command, { stdio: 'inherit' });
    console.log(`✅ ${description} completed`);
    return true;
  } catch (error) {
    console.error(`❌ ${description} failed:`, error.message);
    return false;
  }
}

function checkPrerequisites() {
  console.log('\n🔍 Checking prerequisites...');
  
  const checks = [
    { command: 'node --version', name: 'Node.js' },
    { command: 'npm --version', name: 'npm' },
    { command: 'rustc --version', name: 'Rust' },
    { command: 'cargo --version', name: 'Cargo' }
  ];

  let allGood = true;
  
  checks.forEach(check => {
    try {
      const version = execSync(check.command, { encoding: 'utf8' }).trim();
      console.log(`✅ ${check.name}: ${version}`);
    } catch (error) {
      console.log(`❌ ${check.name}: Not found`);
      allGood = false;
    }
  });

  return allGood;
}

function main() {
  console.log('This script will set up your NestJS application for Tauri desktop development.');
  console.log('');

  // Check prerequisites
  if (!checkPrerequisites()) {
    console.log('\n❌ Prerequisites check failed. Please install missing dependencies:');
    console.log('   - Node.js: https://nodejs.org/');
    console.log('   - Rust: https://rustup.rs/');
    process.exit(1);
  }

  // Install dependencies
  if (!runCommand('npm install', 'Installing Node.js dependencies')) {
    process.exit(1);
  }

  // Build NestJS application
  if (!runCommand('npm run build', 'Building NestJS application')) {
    process.exit(1);
  }

  // Generate icons
  if (!runCommand('node scripts/generate-icons.js', 'Generating placeholder icons')) {
    process.exit(1);
  }

  // Check if Rust dependencies are ready
  if (!runCommand('cd src-tauri && cargo check', 'Checking Rust dependencies')) {
    console.log('\n⚠️  Rust dependencies check failed. This might be normal on first run.');
    console.log('   Tauri will download dependencies on first build.');
  }

  console.log('\n🎉 Setup completed successfully!');
  console.log('');
  console.log('Next steps:');
  console.log('1. Development mode: npm run tauri:dev');
  console.log('2. Build for production: npm run build:tauri');
  console.log('3. Read the full guide: TAURI_IMPLEMENTATION_GUIDE.md');
  console.log('');
  console.log('Happy coding! 🚀');
}

main();
