#!/usr/bin/env node

/**
 * System Check Script
 * Verifies that all requirements are met for running the HungDong POS Server
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🔍 HungDong POS Server - System Check');
console.log('=====================================');
console.log('');

let allChecksPass = true;

// Check 1: Node.js
console.log('1. Checking Node.js...');
try {
  const nodeVersion = execSync('node --version', { encoding: 'utf8' }).trim();
  console.log(`   ✅ Node.js found: ${nodeVersion}`);
  
  // Check if version is recent enough
  const majorVersion = parseInt(nodeVersion.replace('v', '').split('.')[0]);
  if (majorVersion >= 16) {
    console.log(`   ✅ Node.js version is compatible (>= 16)`);
  } else {
    console.log(`   ⚠️  Node.js version may be too old (recommended: >= 16)`);
    allChecksPass = false;
  }
} catch (error) {
  console.log('   ❌ Node.js not found or not accessible');
  console.log('   💡 Please install Node.js from https://nodejs.org/');
  allChecksPass = false;
}

console.log('');

// Check 2: npm
console.log('2. Checking npm...');
try {
  const npmVersion = execSync('npm --version', { encoding: 'utf8' }).trim();
  console.log(`   ✅ npm found: ${npmVersion}`);
} catch (error) {
  console.log('   ❌ npm not found');
  console.log('   💡 npm should be installed with Node.js');
  allChecksPass = false;
}

console.log('');

// Check 3: Project files
console.log('3. Checking project files...');

const requiredFiles = [
  'package.json',
  'dist/main.js',
  'src-tauri/tauri.conf.json'
];

requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`   ✅ ${file} found`);
  } else {
    console.log(`   ❌ ${file} missing`);
    if (file === 'dist/main.js') {
      console.log('   💡 Run "npm run build" to create dist/main.js');
    }
    allChecksPass = false;
  }
});

console.log('');

// Check 4: Dependencies
console.log('4. Checking dependencies...');
if (fs.existsSync('node_modules')) {
  console.log('   ✅ node_modules found');
  
  // Check for key dependencies
  const keyDeps = ['@nestjs/core', '@nestjs/common', 'typeorm', 'sqlite3'];
  keyDeps.forEach(dep => {
    if (fs.existsSync(`node_modules/${dep}`)) {
      console.log(`   ✅ ${dep} installed`);
    } else {
      console.log(`   ⚠️  ${dep} may not be installed`);
    }
  });
} else {
  console.log('   ❌ node_modules not found');
  console.log('   💡 Run "npm install" to install dependencies');
  allChecksPass = false;
}

console.log('');

// Check 5: Port availability
console.log('5. Checking port 3001...');
try {
  const netstatOutput = execSync('lsof -ti:3001', { encoding: 'utf8' }).trim();
  if (netstatOutput) {
    console.log('   ⚠️  Port 3001 is in use');
    console.log(`   💡 Process PID: ${netstatOutput}`);
    console.log('   💡 You may need to stop the existing process');
  } else {
    console.log('   ✅ Port 3001 is available');
  }
} catch (error) {
  console.log('   ✅ Port 3001 is available');
}

console.log('');

// Summary
console.log('📊 System Check Summary');
console.log('======================');

if (allChecksPass) {
  console.log('✅ All checks passed! Your system is ready.');
  console.log('');
  console.log('🚀 You can now run:');
  console.log('   • Development mode: npm run tauri:dev');
  console.log('   • Production server: npm run start:production');
  console.log('   • Build application: npm run build');
} else {
  console.log('❌ Some checks failed. Please address the issues above.');
  console.log('');
  console.log('🔧 Common solutions:');
  console.log('   • Install Node.js: https://nodejs.org/');
  console.log('   • Install dependencies: npm install');
  console.log('   • Build application: npm run build');
}

console.log('');
console.log('📚 For more help, see:');
console.log('   • PRODUCTION_BUILD_SOLUTIONS.md');
console.log('   • README.md');

process.exit(allChecksPass ? 0 : 1);
