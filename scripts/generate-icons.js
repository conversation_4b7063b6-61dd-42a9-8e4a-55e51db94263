#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🎨 Generating placeholder icons for Tauri...');

const iconsDir = path.join(process.cwd(), 'src-tauri', 'icons');

// Ensure icons directory exists
if (!fs.existsSync(iconsDir)) {
  fs.mkdirSync(iconsDir, { recursive: true });
}

// Create placeholder icon files (these would normally be actual image files)
const iconSizes = [
  { name: '32x32.png', size: 32 },
  { name: '128x128.png', size: 128 },
  { name: '<EMAIL>', size: 256 },
  { name: 'icon.ico', size: 256 },
  { name: 'icon.icns', size: 256 }
];

// Create minimal valid RGBA PNG files using a simpler approach
iconSizes.forEach(icon => {
  const iconPath = path.join(iconsDir, icon.name);
  if (!fs.existsSync(iconPath)) {
    if (icon.name.endsWith('.png')) {
      // Create a minimal 1x1 RGBA PNG that <PERSON><PERSON> can read
      const pngData = Buffer.from([
        // PNG signature
        0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A,
        // IHDR chunk
        0x00, 0x00, 0x00, 0x0D, // chunk length
        0x49, 0x48, 0x44, 0x52, // chunk type "IHDR"
        0x00, 0x00, 0x00, 0x01, // width: 1
        0x00, 0x00, 0x00, 0x01, // height: 1
        0x08,                   // bit depth: 8
        0x06,                   // color type: RGBA
        0x00,                   // compression method
        0x00,                   // filter method
        0x00,                   // interlace method
        0x37, 0x6E, 0xF9, 0x24, // CRC
        // IDAT chunk
        0x00, 0x00, 0x00, 0x0A, // chunk length
        0x49, 0x44, 0x41, 0x54, // chunk type "IDAT"
        0x78, 0x9C, 0x63, 0xF8, 0xFF, 0xFF, 0x3F, 0x00, 0x05, 0x00, 0x01, 0x0D, 0x0A, 0x2D, 0xB4, // compressed data
        // IEND chunk
        0x00, 0x00, 0x00, 0x00, // chunk length
        0x49, 0x45, 0x4E, 0x44, // chunk type "IEND"
        0xAE, 0x42, 0x60, 0x82  // CRC
      ]);
      fs.writeFileSync(iconPath, pngData);
    } else {
      // For ICO and ICNS files, create minimal valid files
      fs.writeFileSync(iconPath, Buffer.alloc(1024, 0)); // Minimal placeholder
    }
    console.log(`📄 Created placeholder: ${icon.name}`);
  }
});

console.log('✅ Icon placeholders created');
console.log('⚠️  Note: Replace these with actual icon files before building for production');
console.log('   You can use tools like https://icon.kitchen/ to generate proper icons');
