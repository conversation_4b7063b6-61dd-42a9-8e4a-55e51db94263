#!/usr/bin/env node

/**
 * Production server runner
 * This script runs the NestJS server in production mode without <PERSON><PERSON> build issues
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🚀 Starting HungDong POS Server in Production Mode');
console.log('📁 Project directory:', process.cwd());

// Check if dist/main.js exists
const distMain = path.join(process.cwd(), 'dist', 'main.js');
if (!fs.existsSync(distMain)) {
  console.error('❌ dist/main.js not found! Please run "npm run build" first');
  process.exit(1);
}

// Set up production environment
const productionEnv = {
  ...process.env,
  NODE_ENV: 'production',
  PORT: '3001',
};

console.log('🎯 Starting NestJS server in production mode...');
console.log('📄 Using compiled server: dist/main.js');
console.log('🌐 Server will be available at: http://localhost:3001');
console.log('📊 Admin interface: http://localhost:3001/admin');
console.log('📚 API docs: http://localhost:3001/docs');
console.log('');

// Start the server
const serverProcess = spawn('node', ['dist/main.js'], {
  stdio: 'inherit',
  env: productionEnv,
  cwd: process.cwd()
});

// Handle process events
serverProcess.on('error', (error) => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});

serverProcess.on('exit', (code, signal) => {
  if (code !== 0) {
    console.error(`❌ Server exited with code ${code} and signal ${signal}`);
  } else {
    console.log('✅ Server stopped gracefully');
  }
  process.exit(code || 0);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...');
  serverProcess.kill('SIGTERM');
});

process.on('SIGTERM', () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
  serverProcess.kill('SIGTERM');
});

console.log(`🎯 Server process started with PID: ${serverProcess.pid}`);
console.log('⏳ Server should be ready shortly...');
console.log('');
console.log('💡 Tips:');
console.log('  - Press Ctrl+C to stop the server');
console.log('  - Check the logs above for any startup issues');
console.log('  - The server runs in production mode with optimizations');
