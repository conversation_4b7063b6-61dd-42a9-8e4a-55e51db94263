#!/usr/bin/env node

/**
 * Create a production bundle for Tauri app
 * This creates a self-contained bundle with all necessary dependencies
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('📦 Creating production bundle for Tauri...');

const bundleDir = path.join(process.cwd(), 'tauri-bundle');

// Step 1: Clean and create bundle directory
console.log('🧹 Cleaning bundle directory...');
if (fs.existsSync(bundleDir)) {
  execSync(`rm -rf "${bundleDir}"`, { stdio: 'inherit' });
}
fs.mkdirSync(bundleDir, { recursive: true });

// Step 2: Copy compiled NestJS application
console.log('📋 Copying compiled application...');
const distPath = path.join(process.cwd(), 'dist');
if (!fs.existsSync(distPath)) {
  console.error('❌ dist folder not found! Run npm run build first');
  process.exit(1);
}

execSync(`cp -r "${distPath}" "${bundleDir}/"`, { stdio: 'inherit' });

// Step 3: Copy startup script
console.log('📝 Copying startup script...');
const startupScript = path.join(process.cwd(), 'start-server.js');
if (!fs.existsSync(startupScript)) {
  console.error('❌ start-server.js not found!');
  process.exit(1);
}

fs.copyFileSync(startupScript, path.join(bundleDir, 'start-server.js'));

// Step 4: Create minimal package.json with only production dependencies
console.log('📄 Creating production package.json...');
const originalPackageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

// Essential runtime dependencies for NestJS
const productionDependencies = {
  // Core NestJS
  '@nestjs/common': originalPackageJson.dependencies['@nestjs/common'],
  '@nestjs/core': originalPackageJson.dependencies['@nestjs/core'],
  '@nestjs/platform-express': originalPackageJson.dependencies['@nestjs/platform-express'],
  '@nestjs/serve-static': originalPackageJson.dependencies['@nestjs/serve-static'],
  '@nestjs/config': originalPackageJson.dependencies['@nestjs/config'],
  '@nestjs/typeorm': originalPackageJson.dependencies['@nestjs/typeorm'],
  '@nestjs/jwt': originalPackageJson.dependencies['@nestjs/jwt'],
  '@nestjs/passport': originalPackageJson.dependencies['@nestjs/passport'],
  '@nestjs/terminus': originalPackageJson.dependencies['@nestjs/terminus'],

  // Core dependencies
  'reflect-metadata': originalPackageJson.dependencies['reflect-metadata'],
  'rxjs': originalPackageJson.dependencies['rxjs'],

  // Database
  'sqlite3': originalPackageJson.dependencies['sqlite3'],
  'typeorm': originalPackageJson.dependencies['typeorm'],

  // Validation and transformation
  'class-transformer': originalPackageJson.dependencies['class-transformer'],
  'class-validator': originalPackageJson.dependencies['class-validator'],

  // File handling
  'sharp': originalPackageJson.dependencies['sharp'],
  'multer': originalPackageJson.dependencies['multer'],

  // Authentication
  'bcrypt': originalPackageJson.dependencies['bcrypt'],
  'jsonwebtoken': originalPackageJson.dependencies['jsonwebtoken'],
  'passport': originalPackageJson.dependencies['passport'],
  'passport-jwt': originalPackageJson.dependencies['passport-jwt'],
  'passport-local': originalPackageJson.dependencies['passport-local'],

  // Search
  'minisearch': originalPackageJson.dependencies['minisearch'],

  // Internationalization
  'nestjs-i18n': originalPackageJson.dependencies['nestjs-i18n'],

  // Express and middleware
  'express': originalPackageJson.dependencies['express'],
  'cors': originalPackageJson.dependencies['cors'],
  'helmet': originalPackageJson.dependencies['helmet'],
  'compression': originalPackageJson.dependencies['compression'],
};

// Filter out undefined dependencies
const filteredDependencies = {};
Object.keys(productionDependencies).forEach(key => {
  if (productionDependencies[key]) {
    filteredDependencies[key] = productionDependencies[key];
  }
});

const productionPackageJson = {
  name: originalPackageJson.name,
  version: originalPackageJson.version,
  description: originalPackageJson.description,
  main: 'start-server.js',
  scripts: {
    start: 'node start-server.js'
  },
  dependencies: filteredDependencies,
  engines: originalPackageJson.engines
};

fs.writeFileSync(
  path.join(bundleDir, 'package.json'), 
  JSON.stringify(productionPackageJson, null, 2)
);

// Step 5: Install production dependencies in bundle
console.log('📥 Installing production dependencies...');
try {
  execSync('npm install --production --no-optional', {
    stdio: 'inherit',
    cwd: bundleDir,
    env: {
      ...process.env,
      NODE_ENV: 'production'
    }
  });
} catch (error) {
  console.error('❌ Failed to install production dependencies:', error.message);
  process.exit(1);
}

// Step 6: Copy essential data directories
console.log('📁 Creating data directories...');
const dataDirs = ['data', 'uploads', 'logs'];
dataDirs.forEach(dir => {
  const sourcePath = path.join(process.cwd(), dir);
  const targetPath = path.join(bundleDir, dir);
  
  if (fs.existsSync(sourcePath)) {
    execSync(`cp -r "${sourcePath}" "${targetPath}"`, { stdio: 'inherit' });
  } else {
    fs.mkdirSync(targetPath, { recursive: true });
  }
});

// Step 7: Copy i18n files if they exist
console.log('🌐 Copying i18n files...');
const i18nPath = path.join(process.cwd(), 'src', 'i18n');
if (fs.existsSync(i18nPath)) {
  const targetI18nPath = path.join(bundleDir, 'i18n');
  execSync(`cp -r "${i18nPath}" "${targetI18nPath}"`, { stdio: 'inherit' });
}

// Step 8: Create bundle info
const bundleInfo = {
  name: originalPackageJson.name,
  version: originalPackageJson.version,
  bundleDate: new Date().toISOString(),
  nodeVersion: process.version,
  platform: process.platform,
  arch: process.arch,
  dependencies: Object.keys(filteredDependencies).length,
  bundleSize: 'calculating...'
};

fs.writeFileSync(
  path.join(bundleDir, 'bundle-info.json'),
  JSON.stringify(bundleInfo, null, 2)
);

console.log('✅ Production bundle created successfully!');
console.log('📁 Bundle location:', bundleDir);
console.log('📊 Bundle info:');
console.log(`   - Dependencies: ${Object.keys(filteredDependencies).length}`);
console.log(`   - Node version: ${process.version}`);
console.log(`   - Platform: ${process.platform}`);

// Calculate bundle size
try {
  const sizeOutput = execSync(`du -sh "${bundleDir}"`, { encoding: 'utf8' });
  const size = sizeOutput.split('\t')[0];
  console.log(`   - Bundle size: ${size}`);
  
  // Update bundle info with size
  bundleInfo.bundleSize = size;
  fs.writeFileSync(
    path.join(bundleDir, 'bundle-info.json'),
    JSON.stringify(bundleInfo, null, 2)
  );
} catch (error) {
  console.log('   - Bundle size: Could not calculate');
}

console.log('');
console.log('🎯 Next steps:');
console.log('1. Update tauri.conf.json to use tauri-bundle/* as resources');
console.log('2. Run: npm run tauri:build');
console.log('3. Test the built application');
