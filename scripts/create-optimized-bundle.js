#!/usr/bin/env node

/**
 * Create optimized bundle for Tauri app
 * This creates a smaller bundle by only including essential dependencies
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('📦 Creating optimized bundle for Tauri...');

const bundleDir = path.join(process.cwd(), 'tauri-bundle');

// Step 1: Clean and create bundle directory
console.log('🧹 Cleaning bundle directory...');
if (fs.existsSync(bundleDir)) {
  execSync(`rm -rf "${bundleDir}"`, { stdio: 'inherit' });
}
fs.mkdirSync(bundleDir, { recursive: true });

// Step 2: Copy compiled NestJS application
console.log('📋 Copying compiled application...');
const distSource = path.join(process.cwd(), 'dist');
if (fs.existsSync(distSource)) {
  execSync(`cp -r "${distSource}" "${bundleDir}/"`, { stdio: 'inherit' });
}

// Step 3: Download and bundle only Node.js binary (no full runtime)
console.log('📥 Downloading Node.js binary...');
const NODE_VERSION = 'v18.19.0';
const platform = process.platform;
const arch = process.arch === 'arm64' ? 'arm64' : 'x64';

let nodeUrl;
let nodeBinaryName = 'node';

if (platform === 'darwin') {
  nodeUrl = `https://nodejs.org/dist/${NODE_VERSION}/node-${NODE_VERSION}-darwin-${arch}.tar.gz`;
} else if (platform === 'win32') {
  nodeUrl = `https://nodejs.org/dist/${NODE_VERSION}/node-${NODE_VERSION}-win-${arch}.zip`;
  nodeBinaryName = 'node.exe';
} else {
  nodeUrl = `https://nodejs.org/dist/${NODE_VERSION}/node-${NODE_VERSION}-linux-${arch}.tar.xz`;
}

const nodejsDir = path.join(bundleDir, 'nodejs');
fs.mkdirSync(nodejsDir, { recursive: true });

// Download Node.js
const archiveName = path.basename(nodeUrl);
const archivePath = path.join(nodejsDir, archiveName);

console.log(`📥 Downloading: ${nodeUrl}`);
try {
  execSync(`curl -L "${nodeUrl}" -o "${archivePath}"`, { stdio: 'inherit' });
  console.log(`✅ Downloaded: ${archiveName}`);
} catch (error) {
  console.error('❌ Failed to download Node.js:', error.message);
  process.exit(1);
}

// Extract Node.js
console.log(`📂 Extracting: ${archiveName}`);
const tempDir = path.join(nodejsDir, 'temp');
fs.mkdirSync(tempDir, { recursive: true });

try {
  if (archiveName.endsWith('.tar.gz')) {
    execSync(`tar -xzf "${archivePath}" -C "${tempDir}"`, { stdio: 'inherit' });
  } else if (archiveName.endsWith('.tar.xz')) {
    execSync(`tar -xJf "${archivePath}" -C "${tempDir}"`, { stdio: 'inherit' });
  } else if (archiveName.endsWith('.zip')) {
    execSync(`unzip -q "${archivePath}" -d "${tempDir}"`, { stdio: 'inherit' });
  }
  console.log(`✅ Extracted: ${archiveName}`);
} catch (error) {
  console.error('❌ Failed to extract Node.js:', error.message);
  process.exit(1);
}

// Copy only the Node.js binary
const extractedDirs = fs.readdirSync(tempDir);
const nodeDir = extractedDirs.find(dir => dir.startsWith('node-'));
if (!nodeDir) {
  console.error('❌ Could not find extracted Node.js directory');
  process.exit(1);
}

const sourceBinary = path.join(tempDir, nodeDir, 'bin', nodeBinaryName);
const targetBinary = path.join(nodejsDir, nodeBinaryName);

if (fs.existsSync(sourceBinary)) {
  fs.copyFileSync(sourceBinary, targetBinary);
  if (platform !== 'win32') {
    execSync(`chmod +x "${targetBinary}"`, { stdio: 'inherit' });
  }
  console.log(`✅ Node.js binary copied: ${targetBinary}`);
} else {
  // Windows case - binary might be in root
  const altSourceBinary = path.join(tempDir, nodeDir, nodeBinaryName);
  if (fs.existsSync(altSourceBinary)) {
    fs.copyFileSync(altSourceBinary, targetBinary);
    console.log(`✅ Node.js binary copied: ${targetBinary}`);
  } else {
    console.error('❌ Could not find Node.js binary');
    process.exit(1);
  }
}

// Clean up
execSync(`rm -rf "${tempDir}" "${archivePath}"`, { stdio: 'inherit' });

// Step 4: Create minimal package.json with only essential dependencies
console.log('📄 Creating production package.json...');
const originalPackageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));

const essentialDependencies = {
  '@nestjs/common': originalPackageJson.dependencies['@nestjs/common'],
  '@nestjs/core': originalPackageJson.dependencies['@nestjs/core'],
  '@nestjs/platform-express': originalPackageJson.dependencies['@nestjs/platform-express'],
  'reflect-metadata': originalPackageJson.dependencies['reflect-metadata'],
  'rxjs': originalPackageJson.dependencies['rxjs'],
  'sqlite3': originalPackageJson.dependencies['sqlite3'],
  'typeorm': originalPackageJson.dependencies['typeorm'],
};

// Filter out undefined dependencies
const filteredDependencies = {};
Object.keys(essentialDependencies).forEach(key => {
  if (essentialDependencies[key]) {
    filteredDependencies[key] = essentialDependencies[key];
  }
});

const productionPackageJson = {
  name: originalPackageJson.name,
  version: originalPackageJson.version,
  main: 'start-server.js',
  dependencies: filteredDependencies
};

fs.writeFileSync(
  path.join(bundleDir, 'package.json'), 
  JSON.stringify(productionPackageJson, null, 2)
);

// Step 5: Install only essential dependencies
console.log('📦 Installing essential dependencies...');
try {
  execSync('npm install --production --no-optional', {
    stdio: 'inherit',
    cwd: bundleDir,
    env: { ...process.env, NODE_ENV: 'production' }
  });
} catch (error) {
  console.error('❌ Failed to install dependencies:', error.message);
  process.exit(1);
}

// Step 6: Create optimized startup script
console.log('📝 Creating optimized startup script...');
const startupScript = `#!/usr/bin/env node

/**
 * Optimized startup script with bundled Node.js binary
 */

const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

const appDir = __dirname;
console.log('🚀 Starting HungDong POS Server with bundled Node.js...');

// Use bundled Node.js binary
const nodeBinary = path.join(appDir, 'nodejs', '${nodeBinaryName}');
const mainJs = path.join(appDir, 'dist', 'main.js');

if (!fs.existsSync(nodeBinary)) {
  console.error('❌ Bundled Node.js not found:', nodeBinary);
  process.exit(1);
}

if (!fs.existsSync(mainJs)) {
  console.error('❌ Server file not found:', mainJs);
  process.exit(1);
}

console.log('🎯 Using bundled Node.js:', nodeBinary);
console.log('🎯 Starting server:', mainJs);

// Create wrapper script to fix crypto issues
const wrapperScript = \`
// Fix crypto global issue
if (!global.crypto) {
  global.crypto = require('crypto');
}

// Load and start the main application
require('\${mainJs}');
\`;

const wrapperPath = path.join(appDir, 'server-wrapper.js');
fs.writeFileSync(wrapperPath, wrapperScript);

// Start server
const serverProcess = spawn(nodeBinary, [wrapperPath], {
  stdio: 'inherit',
  env: {
    ...process.env,
    NODE_ENV: 'production',
    PORT: '3001'
  },
  cwd: appDir
});

serverProcess.on('error', (error) => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});

serverProcess.on('exit', (code, signal) => {
  if (code !== 0) {
    console.error(\`❌ Server exited with code \${code}\`);
  }
  process.exit(code || 0);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('🛑 Shutting down...');
  serverProcess.kill('SIGTERM');
});

process.on('SIGTERM', () => {
  console.log('🛑 Shutting down...');
  serverProcess.kill('SIGTERM');
});

console.log(\`🎯 Server started with PID: \${serverProcess.pid}\`);
console.log('⏳ Server should be available at http://localhost:3001 shortly...');
`;

fs.writeFileSync(path.join(bundleDir, 'start-server.js'), startupScript);

// Step 7: Create data directories
console.log('📁 Creating data directories...');
const dataDirs = ['data', 'uploads', 'logs'];
dataDirs.forEach(dir => {
  fs.mkdirSync(path.join(bundleDir, dir), { recursive: true });
});

console.log('✅ Optimized bundle created successfully!');

// Calculate bundle size
try {
  const sizeOutput = execSync(`du -sh "${bundleDir}"`, { encoding: 'utf8' });
  const size = sizeOutput.split('\t')[0];
  console.log(`📊 Bundle size: ${size} (optimized)`);
} catch (error) {
  console.log('📊 Bundle size: Could not calculate');
}

console.log('📁 Bundle location:', bundleDir);
console.log('🎯 Node.js binary:', path.join(bundleDir, 'nodejs', nodeBinaryName));
console.log('');
console.log('🎯 This optimized bundle:');
console.log('- Contains only Node.js binary (not full runtime)');
console.log('- Includes only essential dependencies');
console.log('- Much smaller size for Tauri builds');
console.log('- Self-contained and portable');
