#!/usr/bin/env node

/**
 * Custom server start script for Tauri app
 * This starts the NestJS server without file watching to prevent auto-restarts
 */

const { spawn } = require('child_process');
const path = require('path');

// Ensure we're in the project root
const projectRoot = path.resolve(__dirname, '..');
process.chdir(projectRoot);

console.log('🚀 Starting NestJS server without file watching...');
console.log('📁 Project root:', projectRoot);

// Start the server using nest start (without --watch)
const serverProcess = spawn('npx', ['nest', 'start'], {
  stdio: 'inherit',
  env: {
    ...process.env,
    NODE_ENV: 'development',
    PORT: '3001'
  }
});

// Handle process termination
process.on('SIGTERM', () => {
  console.log('📴 Received SIGTERM, stopping server...');
  serverProcess.kill('SIGTERM');
});

process.on('SIGINT', () => {
  console.log('📴 Received SIGINT, stopping server...');
  serverProcess.kill('SIGINT');
});

serverProcess.on('close', (code) => {
  console.log(`📴 Server process exited with code ${code}`);
  process.exit(code);
});

serverProcess.on('error', (error) => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});
