// Prevents additional console window on Windows in release, DO NOT REMOVE!!
#![cfg_attr(not(debug_assertions), windows_subsystem = "windows")]

use std::sync::{Arc, Mutex};
use std::thread;
use std::time::Duration;
use tauri::State;
use log::{info, error};

mod server_manager;
mod system_info;

use server_manager::{ServerManager, ServerStatus, LogEntry};

// <PERSON><PERSON> commands
#[tauri::command]
async fn start_server(server_manager: State<'_, Arc<Mutex<ServerManager>>>) -> Result<bool, String> {
    let manager_clone = Arc::clone(&server_manager);
    tokio::task::spawn_blocking(move || {
        let mut manager = manager_clone.lock().map_err(|e| e.to_string())?;
        tokio::runtime::Handle::current().block_on(manager.start()).map_err(|e| e.to_string())
    }).await.map_err(|e| e.to_string())?
}

#[tauri::command]
async fn stop_server(server_manager: State<'_, Arc<Mutex<ServerManager>>>) -> Result<bool, String> {
    let manager_clone = Arc::clone(&server_manager);
    tokio::task::spawn_blocking(move || {
        let mut manager = manager_clone.lock().map_err(|e| e.to_string())?;
        tokio::runtime::Handle::current().block_on(manager.stop()).map_err(|e| e.to_string())
    }).await.map_err(|e| e.to_string())?
}

#[tauri::command]
async fn restart_server(server_manager: State<'_, Arc<Mutex<ServerManager>>>) -> Result<bool, String> {
    let manager_clone = Arc::clone(&server_manager);
    tokio::task::spawn_blocking(move || {
        let mut manager = manager_clone.lock().map_err(|e| e.to_string())?;
        tokio::runtime::Handle::current().block_on(manager.restart()).map_err(|e| e.to_string())
    }).await.map_err(|e| e.to_string())?
}

#[tauri::command]
fn get_server_status(server_manager: State<'_, Arc<Mutex<ServerManager>>>) -> Result<ServerStatus, String> {
    let manager = server_manager.lock().map_err(|e| e.to_string())?;
    Ok(manager.get_status())
}

#[tauri::command]
fn get_server_logs(server_manager: State<'_, Arc<Mutex<ServerManager>>>) -> Result<Vec<LogEntry>, String> {
    let manager = server_manager.lock().map_err(|e| e.to_string())?;
    Ok(manager.get_logs())
}

#[tauri::command]
fn open_browser_url(app: tauri::AppHandle, url: String) -> Result<(), String> {
    use tauri_plugin_opener::OpenerExt;
    app.opener().open_url(url, None::<String>).map_err(|e| e.to_string())
}

fn main() {
    env_logger::init();
    
    let server_manager = Arc::new(Mutex::new(ServerManager::new()));
    
    // Auto-start server in a separate thread
    let manager_clone = Arc::clone(&server_manager);
    thread::spawn(move || {
        thread::sleep(Duration::from_secs(2)); // Wait for app to initialize
        if let Ok(mut manager) = manager_clone.lock() {
            if let Err(e) = tokio::runtime::Runtime::new().unwrap().block_on(manager.start()) {
                error!("Failed to auto-start server: {}", e);
            }
        }
    });

    tauri::Builder::default()
        .plugin(tauri_plugin_opener::init())
        .plugin(tauri_plugin_fs::init())
        .manage(server_manager)
        .invoke_handler(tauri::generate_handler![
            start_server,
            stop_server,
            restart_server,
            get_server_status,
            get_server_logs,
            open_browser_url
        ])
        .setup(|_app| {
            info!("HungDong POS Server Desktop App starting...");
            Ok(())
        })
        .run(tauri::generate_context!())
        .expect("error while running tauri application");
}
