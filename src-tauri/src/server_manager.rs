use std::process::{Child, Command, Stdio};
use std::time::SystemTime;
use std::collections::VecDeque;
use serde::{Deserialize, Serialize};
use log::{info, error, warn};
use tokio::time::{sleep, Duration};

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ServerStatus {
    pub running: bool,
    pub port: Option<u16>,
    pub pid: Option<u32>,
    pub uptime: Option<u64>,
    pub last_error: Option<String>,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct LogEntry {
    pub timestamp: String,
    pub level: String,
    pub message: String,
}

pub struct ServerManager {
    server_process: Option<Child>,
    status: ServerStatus,
    logs: VecDeque<LogEntry>,
    start_time: Option<SystemTime>,
}

impl ServerManager {
    pub fn new() -> Self {
        Self {
            server_process: None,
            status: ServerStatus {
                running: false,
                port: None,
                pid: None,
                uptime: None,
                last_error: None,
            },
            logs: VecDeque::with_capacity(1000),
            start_time: None,
        }
    }

    pub async fn start(&mut self) -> Result<bool, Box<dyn std::error::Error>> {
        if self.status.running {
            self.add_log("INFO", "Server is already running");
            return Ok(true);
        }

        self.add_log("INFO", "Starting NestJS server...");

        // Determine if we're in development or production
        // For now, always use development mode to avoid production bundle issues
        let is_dev = true; // cfg!(debug_assertions);

        if is_dev {
            // Development mode - use npm run start:dev
            self.add_log("INFO", "Starting server in development mode...");

            // First, make sure no server is already running on port 3001
            self.add_log("INFO", "Checking if port 3001 is already in use...");

            #[cfg(unix)]
            {
                use std::process::Command;
                if let Ok(output) = Command::new("lsof").args(&["-ti:3001"]).output() {
                    let pids = String::from_utf8_lossy(&output.stdout);
                    for pid_str in pids.lines() {
                        if let Ok(existing_pid) = pid_str.trim().parse::<u32>() {
                            self.add_log("WARN", &format!("Killing existing process on port 3001 (PID: {})", existing_pid));
                            let _ = Command::new("kill").args(&["-9", &existing_pid.to_string()]).output();
                        }
                    }
                }
            }

            #[cfg(windows)]
            {
                use std::process::Command;
                if let Ok(output) = Command::new("netstat").args(&["-ano"]).output() {
                    let netstat_output = String::from_utf8_lossy(&output.stdout);
                    for line in netstat_output.lines() {
                        if line.contains(":3001") && line.contains("LISTENING") {
                            if let Some(pid_str) = line.split_whitespace().last() {
                                if let Ok(existing_pid) = pid_str.parse::<u32>() {
                                    self.add_log("WARN", &format!("Killing existing process on port 3001 (PID: {})", existing_pid));
                                    let _ = Command::new("taskkill").args(&["/F", "/PID", &existing_pid.to_string()]).output();
                                }
                            }
                        }
                    }
                }
            }

            // Wait a moment for any killed processes to fully terminate
            sleep(Duration::from_secs(1)).await;

            // Find the project root (where package.json is)
            let exe_path = std::env::current_exe()?;
            let exe_dir = exe_path.parent().unwrap();

            self.add_log("INFO", &format!("Executable location: {:?}", exe_dir));

            // Check if we're in a built app bundle (production) or development
            let is_built_app = exe_dir.to_string_lossy().contains(".app/Contents/MacOS") ||
                              exe_dir.to_string_lossy().contains("target/release") ||
                              exe_dir.to_string_lossy().contains("target/debug");

            let project_root = if is_built_app {
                self.add_log("INFO", "Detected built application - using bundled resources");

                // For macOS apps, resources are in Contents/Resources/_up_/tauri-bundle/
                // For other platforms, they're in the same directory as the executable
                let bundle_dir = if cfg!(target_os = "macos") && exe_dir.to_string_lossy().contains(".app/Contents/MacOS") {
                    let app_contents = exe_dir.parent().unwrap(); // Contents
                    let resources_dir = app_contents.join("Resources").join("_up_").join("tauri-bundle");
                    self.add_log("INFO", &format!("macOS app bundle detected, looking in: {:?}", resources_dir));
                    resources_dir
                } else {
                    exe_dir.to_path_buf()
                };

                // Check if bundled resources exist
                if bundle_dir.join("package.json").exists() && bundle_dir.join("dist").join("main.js").exists() {
                    self.add_log("INFO", &format!("Found bundled resources in: {:?}", bundle_dir));
                    bundle_dir
                } else {
                    // Fallback: try to find the development project directory
                    self.add_log("WARN", "Bundled resources not found, searching for development project...");
                    let mut search_path = exe_dir.to_path_buf();
                    let mut found_project = false;

                    // Search up to 10 levels up for the project directory
                    for _ in 0..10 {
                        if search_path.join("package.json").exists() &&
                           search_path.join("dist").join("main.js").exists() {
                            found_project = true;
                            break;
                        }
                        if let Some(parent) = search_path.parent() {
                            search_path = parent.to_path_buf();
                        } else {
                            break;
                        }
                    }

                    if found_project {
                        self.add_log("INFO", &format!("Found development project directory: {:?}", search_path));
                        search_path
                    } else {
                        return Err("Built application cannot find bundled resources or development project directory. Please ensure the app is built correctly or run from the project directory.".into());
                    }
                }
            } else {
                // Development mode - find project root normally
                let mut project_root = exe_dir.to_path_buf();

                // Go up directories until we find package.json
                while !project_root.join("package.json").exists() && project_root.parent().is_some() {
                    project_root = project_root.parent().unwrap().to_path_buf();
                }

                if !project_root.join("package.json").exists() {
                    return Err("Could not find project root with package.json".into());
                }

                project_root
            };

            self.add_log("INFO", &format!("Starting server in: {:?}", project_root));

            // Check if we have a pre-compiled version for faster startup
            let dist_main = project_root.join("dist").join("main.js");

            self.add_log("INFO", &format!("Looking for dist/main.js at: {:?}", dist_main));

            // Check for standalone executable first, then fallback to Node.js
            let executable_name = if cfg!(target_os = "windows") {
                "hungdong-pos-server.exe"
            } else {
                "hungdong-pos-server"
            };

            let standalone_executable = project_root.join(executable_name);

            let mut cmd = if standalone_executable.exists() {
                self.add_log("INFO", &format!("Using standalone executable: {:?}", standalone_executable));
                let mut c = Command::new(&standalone_executable);
                c
            } else {
                // Fallback to Node.js approach
                self.add_log("INFO", "Standalone executable not found, falling back to Node.js");

                // Check for bundled Node.js first, then system Node.js
                let bundled_node = if cfg!(target_os = "windows") {
                    project_root.join("nodejs").join("bin").join("node.exe")
                } else {
                    project_root.join("nodejs").join("bin").join("node")
                };

                let (node_cmd, node_available) = if bundled_node.exists() {
                    self.add_log("INFO", &format!("Using bundled Node.js: {:?}", bundled_node));
                    (bundled_node.to_string_lossy().to_string(), true)
                } else {
                    // Check system Node.js
                    let system_node_available = Command::new("node").arg("--version").output().is_ok();
                    if system_node_available {
                        self.add_log("INFO", "Using system Node.js");
                        ("node".to_string(), true)
                    } else {
                        self.add_log("WARN", "No Node.js found (neither bundled nor system)");
                        ("node".to_string(), false)
                    }
                };

                if !node_available {
                    let error_msg = if is_built_app {
                        "Neither standalone executable nor Node.js is available. Please rebuild the application with the executable build process."
                    } else {
                        "Node.js is not installed or not available in PATH. Please install Node.js to run the server."
                    };
                    self.add_log("ERROR", error_msg);
                    return Err(error_msg.into());
                }

                if dist_main.exists() {
                    self.add_log("INFO", "Using pre-compiled dist/main.js");
                    let mut c = Command::new(&node_cmd);
                    c.arg(&dist_main);
                    c
                } else {
                    self.add_log("INFO", "Using nest start (compiling on startup)");
                    let mut c = Command::new("npx");
                    c.arg("nest").arg("start");
                    c
                }
            };

            cmd.current_dir(&project_root)
               .stdout(Stdio::inherit()) // Let process output to console
               .stderr(Stdio::inherit()) // Let process output to console
               .env("NODE_ENV", "development")
               .env("PORT", "3001");

            self.add_log("INFO", &format!("Starting server in {}", project_root.display()));

            self.add_log("INFO", &format!("Executing command: {:?}", cmd));

            match cmd.spawn() {
                Ok(child) => {
                    let pid = child.id();
                    self.add_log("INFO", &format!("Process spawned successfully with PID: {}", pid));

                    self.server_process = Some(child);
                    self.status.running = true;
                    self.status.port = Some(3001);
                    self.status.pid = Some(pid);
                    self.start_time = Some(SystemTime::now());
                    self.status.last_error = None;

                    self.add_log("INFO", &format!("NestJS development server process started (PID: {})", pid));
                    self.add_log("INFO", "Waiting for server to be ready...");

                    // Give the process a moment to initialize before starting health checks
                    self.add_log("INFO", "Giving process 2 seconds to initialize...");
                    sleep(Duration::from_secs(2)).await;

                    // Wait for the server to actually be ready with optimized polling
                    let mut attempts = 0;
                    let max_attempts = 30; // 30 seconds max wait
                    let mut delay = 1000; // Start with 1s delay

                    while attempts < max_attempts {
                        // Use progressive delay to reduce resource usage
                        sleep(Duration::from_millis(delay)).await;

                        if self.check_server_health().await {
                            self.add_log("INFO", &format!("✅ NestJS server is ready and responding! (took {}ms)", attempts * delay));
                            return Ok(true);
                        }

                        attempts += 1;

                        // Progressive backoff: 500ms -> 1s -> 1.5s -> 2s (max)
                        delay = std::cmp::min(delay + 250, 2000);

                        if attempts % 3 == 0 {
                            self.add_log("INFO", &format!("Still waiting for server... ({}/{})", attempts, max_attempts));
                        }

                        // Only check if process is still running after several attempts
                        // This gives the Node.js process time to fully initialize
                        if attempts > 5 && attempts % 5 == 0 {
                            if let Some(ref mut process) = self.server_process {
                                if let Ok(Some(exit_status)) = process.try_wait() {
                                    let error_msg = format!("Server process exited unexpectedly during startup with status: {:?}", exit_status);
                                    self.add_log("ERROR", &error_msg);
                                    self.status.last_error = Some(error_msg.clone());
                                    self.status.running = false;
                                    return Err(error_msg.into());
                                }
                            }
                        }
                    }

                    // If we get here, server didn't respond in time
                    self.add_log("WARN", "Server started but not responding to health checks yet");
                    self.add_log("INFO", "You may need to wait a few more seconds for the server to be fully ready");
                    Ok(true)
                }
                Err(e) => {
                    let error_msg = format!("Failed to start development server: {}", e);
                    self.add_log("ERROR", &error_msg);
                    self.status.last_error = Some(error_msg.clone());
                    Err(error_msg.into())
                }
            }
        } else {
            // Production mode - use compiled JavaScript
            self.add_log("INFO", "Starting server in production mode...");

            let exe_path = std::env::current_exe()?;
            let exe_dir = exe_path.parent().unwrap();

            self.add_log("INFO", &format!("Looking for server files in: {:?}", exe_dir));

            // For macOS apps, resources are in Contents/Resources/_up_/
            // For other platforms, they're in the same directory as the executable
            let bundle_dir = if cfg!(target_os = "macos") {
                // Check if we're in a macOS app bundle
                if exe_dir.to_string_lossy().contains(".app/Contents/MacOS") {
                    let app_contents = exe_dir.parent().unwrap(); // Contents
                    let resources_dir = app_contents.join("Resources").join("_up_").join("tauri-bundle");
                    self.add_log("INFO", &format!("macOS app bundle detected, looking in: {:?}", resources_dir));
                    resources_dir
                } else {
                    exe_dir.to_path_buf()
                }
            } else {
                exe_dir.to_path_buf()
            };

            // Look for the startup script in the bundle directory
            let startup_script = bundle_dir.join("start-server.js");

            // Check if we have the production bundle structure
            let bundle_main_js = bundle_dir.join("dist").join("main.js");

            let (node_cmd, script_path, working_dir) = if startup_script.exists() {
                self.add_log("INFO", "Using startup script for production server");
                // For minimal bundle, we need to run from the app's parent directory
                // so Node.js can find the node_modules
                let working_dir = if cfg!(target_os = "macos") && exe_dir.to_string_lossy().contains(".app/Contents/MacOS") {
                    // For macOS app bundle, we need to find the project directory
                    // The app is usually installed in /Applications, but we need the project dir
                    // For now, use the bundle directory and let the script handle NODE_PATH
                    bundle_dir.clone()
                } else {
                    bundle_dir.clone()
                };
                ("node".to_string(), startup_script, working_dir)
            } else if bundle_main_js.exists() {
                self.add_log("INFO", "Using direct main.js execution");
                ("node".to_string(), bundle_main_js, bundle_dir.clone())
            } else {
                let error_msg = format!("Production server files not found in {:?}", bundle_dir);
                self.add_log("ERROR", &error_msg);
                return Err(error_msg.into());
            };

            // Start the NestJS server
            let mut cmd = Command::new(&node_cmd);
            cmd.arg(&script_path);

            cmd.current_dir(&working_dir)
               .stdout(Stdio::inherit()) // Let process output to console for debugging
               .stderr(Stdio::inherit()) // Let process output to console for debugging
               .env("NODE_ENV", "production")
               .env("PORT", "3001");

            match cmd.spawn() {
                Ok(child) => {
                    let pid = child.id();
                    self.server_process = Some(child);
                    self.status.running = true;
                    self.status.port = Some(3001);
                    self.status.pid = Some(pid);
                    self.start_time = Some(SystemTime::now());
                    self.status.last_error = None;

                    self.add_log("INFO", &format!("NestJS production server process started (PID: {})", pid));
                    self.add_log("INFO", "Waiting for server to be ready...");

                    // Wait for the server to actually be ready
                    let mut attempts = 0;
                    let max_attempts = 20; // 20 seconds for production

                    while attempts < max_attempts {
                        sleep(Duration::from_secs(1)).await;

                        if self.check_server_health().await {
                            self.add_log("INFO", "✅ NestJS production server is ready and responding!");
                            return Ok(true);
                        }

                        attempts += 1;
                        if attempts % 5 == 0 {
                            self.add_log("INFO", &format!("Still waiting for server... ({}/{})", attempts, max_attempts));
                        }
                    }

                    self.add_log("WARN", "Server started but not responding to health checks yet");
                    Ok(true)
                }
                Err(e) => {
                    let error_msg = format!("Failed to start production server: {}", e);
                    self.add_log("ERROR", &error_msg);
                    self.status.last_error = Some(error_msg.clone());
                    Err(error_msg.into())
                }
            }
        }
    }

    pub async fn stop(&mut self) -> Result<bool, Box<dyn std::error::Error>> {
        if !self.status.running {
            self.add_log("INFO", "Server is not running");
            return Ok(true);
        }

        self.add_log("INFO", "Stopping NestJS server...");

        if let Some(mut child) = self.server_process.take() {
            let pid = child.id();

            // For npm processes, we need to kill the entire process tree
            let is_dev = true; // cfg!(debug_assertions);

            if is_dev {
                // In development, we started with npm, so we need to kill the npm process tree
                self.add_log("INFO", &format!("Killing development server process tree (PID: {})", pid));

                #[cfg(unix)]
                {
                    // On Unix systems, kill the entire process group
                    use std::process::Command;
                    let _ = Command::new("pkill")
                        .args(&["-P", &pid.to_string()])
                        .output();

                    // Also try to kill by port to ensure nothing is left running
                    let _ = Command::new("lsof")
                        .args(&["-ti:3001"])
                        .output()
                        .and_then(|output| {
                            let pids = String::from_utf8_lossy(&output.stdout);
                            for pid_str in pids.lines() {
                                if let Ok(port_pid) = pid_str.trim().parse::<u32>() {
                                    let _ = Command::new("kill")
                                        .args(&["-9", &port_pid.to_string()])
                                        .output();
                                }
                            }
                            Ok(output)
                        });
                }

                #[cfg(windows)]
                {
                    // On Windows, use taskkill to kill the process tree
                    use std::process::Command;
                    let _ = Command::new("taskkill")
                        .args(&["/F", "/T", "/PID", &pid.to_string()])
                        .output();
                }
            }

            // Kill the main process
            match child.kill() {
                Ok(_) => {
                    let _ = child.wait();

                    // Additional cleanup - wait a bit for processes to fully terminate
                    sleep(Duration::from_secs(2)).await;

                    self.status.running = false;
                    self.status.pid = None;
                    self.status.uptime = None;
                    self.start_time = None;
                    self.add_log("INFO", "Server stopped successfully");
                    Ok(true)
                }
                Err(e) => {
                    let error_msg = format!("Failed to stop server: {}", e);
                    self.add_log("ERROR", &error_msg);
                    Err(Box::new(e))
                }
            }
        } else {
            self.status.running = false;
            self.add_log("INFO", "Server process was not found, marked as stopped");
            Ok(true)
        }
    }

    pub async fn restart(&mut self) -> Result<bool, Box<dyn std::error::Error>> {
        self.add_log("INFO", "Restarting server...");
        self.stop().await?;
        sleep(Duration::from_secs(1)).await;
        self.start().await
    }

    pub fn get_status(&self) -> ServerStatus {
        let mut status = self.status.clone();
        
        if let Some(start_time) = self.start_time {
            if let Ok(duration) = SystemTime::now().duration_since(start_time) {
                status.uptime = Some(duration.as_secs());
            }
        }
        
        status
    }

    pub fn get_logs(&self) -> Vec<LogEntry> {
        self.logs.iter().cloned().collect()
    }

    async fn check_server_health(&self) -> bool {
        // Create a client with optimized settings for health checks
        let client = reqwest::Client::builder()
            .timeout(Duration::from_secs(2)) // 2 second timeout
            .connect_timeout(Duration::from_millis(500)) // 500ms connect timeout
            .pool_idle_timeout(Duration::from_secs(10)) // Keep connections alive
            .pool_max_idle_per_host(2) // Limit idle connections
            .build()
            .unwrap_or_else(|_| reqwest::Client::new());

        // Try health endpoint first (most efficient)
        match client.get("http://localhost:3001/v1/health").send().await {
            Ok(response) => {
                if response.status().is_success() {
                    return true;
                }
            }
            Err(_) => {}
        }

        // If health endpoint doesn't exist, try the root API endpoint
        match client.get("http://localhost:3001/api/v1").send().await {
            Ok(response) => response.status().is_success(),
            Err(_) => false,
        }
    }

    fn add_log(&mut self, level: &str, message: &str) {
        let timestamp = chrono::Utc::now().format("%Y-%m-%d %H:%M:%S").to_string();
        let log_entry = LogEntry {
            timestamp,
            level: level.to_string(),
            message: message.to_string(),
        };

        if self.logs.len() >= 1000 {
            self.logs.pop_front();
        }
        self.logs.push_back(log_entry);

        // Also log to console
        match level {
            "ERROR" => error!("{}", message),
            "WARN" => warn!("{}", message),
            _ => info!("{}", message),
        }
    }
}
