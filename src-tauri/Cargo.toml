[package]
name = "hungdong-pos-server"
version = "0.0.1"
description = "HungDong POS Server Desktop Application"
authors = ["HungDong Team"]
license = "UNLICENSED"
repository = ""
default-run = "hungdong-pos-server"
edition = "2021"
rust-version = "1.60"

[build-dependencies]
tauri-build = { version = "2.0", features = [] }

[dependencies]
serde_json = "1.0"
serde = { version = "1.0", features = ["derive"] }
tauri = { version = "2.1", features = [] }
tauri-plugin-opener = "2.0"
tauri-plugin-fs = "2.0"
tokio = { version = "1.0", features = ["full"] }
reqwest = { version = "0.11", features = ["json"] }
log = "0.4"
env_logger = "0.10"
chrono = { version = "0.4", features = ["serde"] }
hostname = "0.3"

[features]
# this feature is used for production builds or when `devPath` points to the filesystem and the built-in dev server is disabled.
# If you use cargo directly instead of tauri's cli you can use this feature flag to switch between tauri's `dev` and `build` modes.
# DO NOT REMOVE!!
custom-protocol = [ "tauri/custom-protocol" ]
